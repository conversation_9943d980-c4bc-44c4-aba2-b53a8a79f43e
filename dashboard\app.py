"""
Hina Bot Dashboard - Main FastAPI Application
A clean, modern dashboard for Discord bot management with OAuth2 authentication
"""

from fastapi import FastAP<PERSON>, Request, Depends, HTTPException, status
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON>ear<PERSON>, HTTPAuthorizationCredentials
import asyncio
import asyncpg
import json
import logging
from typing import Optional, List, Dict, Any
import aiohttp
from datetime import datetime, timedelta
import jwt
import os
from pathlib import Path

# Import our custom modules
from auth import DiscordOAuth, get_current_user, verify_guild_permissions
from database import DatabaseManager
from bot_interface import BotInterface
from models import User, Guild, Command, Setting
from api_routes import api, set_dependencies

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Hina Bot Dashboard",
    description="Modern Discord Bot Management Dashboard",
    version="1.0.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc"
)

# CORS middleware for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],  # React dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Include API routes
app.include_router(api)

# Global instances
db_manager: Optional[DatabaseManager] = None
bot_interface: Optional[BotInterface] = None
discord_oauth: Optional[DiscordOAuth] = None

@app.on_event("startup")
async def startup_event():
    """Initialize database and bot connections on startup"""
    global db_manager, bot_interface, discord_oauth
    
    try:
        # Initialize database manager
        db_manager = DatabaseManager()
        await db_manager.initialize()
        
        # Initialize bot interface
        bot_interface = BotInterface()
        await bot_interface.initialize()
        
        # Initialize Discord OAuth
        discord_oauth = DiscordOAuth()

        # Set dependencies for API routes
        set_dependencies(db_manager, bot_interface)

        logger.info("Dashboard startup completed successfully")
        
    except Exception as e:
        logger.error(f"Failed to initialize dashboard: {e}")
        raise

@app.on_event("shutdown")
async def shutdown_event():
    """Clean up connections on shutdown"""
    global db_manager, bot_interface
    
    if db_manager:
        await db_manager.close()
    if bot_interface:
        await bot_interface.close()
        
    logger.info("Dashboard shutdown completed")

# Root route - serve React app
@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Serve the main React application"""
    return templates.TemplateResponse("index.html", {"request": request})

# Authentication routes
@app.get("/auth/login")
async def login():
    """Redirect to Discord OAuth2 authorization"""
    auth_url = discord_oauth.get_authorization_url()
    return RedirectResponse(url=auth_url)

@app.get("/auth/callback")
async def auth_callback(code: str, state: Optional[str] = None):
    """Handle Discord OAuth2 callback"""
    try:
        # Exchange code for access token
        token_data = await discord_oauth.exchange_code_for_token(code)
        
        # Get user information
        user_data = await discord_oauth.get_user_info(token_data["access_token"])
        
        # Get user's guilds
        guilds_data = await discord_oauth.get_user_guilds(token_data["access_token"])
        
        # Filter guilds where user has admin permissions
        admin_guilds = [
            guild for guild in guilds_data 
            if (guild["permissions"] & 0x8) == 0x8  # Administrator permission
        ]
        
        # Create JWT token for our dashboard
        jwt_token = create_jwt_token(user_data, admin_guilds)
        
        # Redirect to dashboard with token
        response = RedirectResponse(url="/dashboard")
        response.set_cookie(
            key="auth_token",
            value=jwt_token,
            httponly=True,
            secure=True,
            samesite="lax",
            max_age=86400  # 24 hours
        )
        
        return response
        
    except Exception as e:
        logger.error(f"OAuth callback error: {e}")
        raise HTTPException(status_code=400, detail="Authentication failed")

@app.post("/auth/logout")
async def logout():
    """Logout user and clear authentication"""
    response = RedirectResponse(url="/")
    response.delete_cookie("auth_token")
    return response

# Dashboard routes
@app.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, user: User = Depends(get_current_user)):
    """Main dashboard page"""
    return templates.TemplateResponse("dashboard.html", {
        "request": request,
        "user": user
    })

# API routes
@app.get("/api/user/me")
async def get_current_user_info(user: User = Depends(get_current_user)):
    """Get current authenticated user information"""
    return user

@app.get("/api/user/guilds")
async def get_user_guilds(user: User = Depends(get_current_user)):
    """Get guilds where user has admin permissions"""
    return user.admin_guilds

@app.get("/api/guild/{guild_id}")
async def get_guild_info(
    guild_id: int,
    user: User = Depends(get_current_user)
):
    """Get detailed guild information"""
    # Verify user has access to this guild
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    # Get guild info from bot
    guild_info = await bot_interface.get_guild_info(guild_id)
    if not guild_info:
        raise HTTPException(status_code=404, detail="Guild not found")
    
    return guild_info

def create_jwt_token(user_data: Dict[str, Any], admin_guilds: List[Dict[str, Any]]) -> str:
    """Create JWT token for authenticated user"""
    payload = {
        "user_id": user_data["id"],
        "username": user_data["username"],
        "discriminator": user_data["discriminator"],
        "avatar": user_data["avatar"],
        "admin_guilds": admin_guilds,
        "exp": datetime.utcnow() + timedelta(hours=24),
        "iat": datetime.utcnow()
    }
    
    # Use a secure secret key in production
    secret_key = os.getenv("JWT_SECRET", "your-secret-key-change-in-production")
    return jwt.encode(payload, secret_key, algorithm="HS256")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
