"""
API routes for the dashboard
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Dict, Any, Optional
import logging

from auth import get_current_user, verify_guild_permissions
from database import DatabaseManager
from bot_interface import BotInterface
from models import (
    User, Setting, Command, CommandCategory, ApiResponse,
    SettingUpdateRequest, PrefixUpdateRequest, CommandExecuteRequest,
    LoggingConfigRequest, AutomodConfigRequest, MuteRoleRequest
)

logger = logging.getLogger(__name__)

# Create API router
api = APIRouter(prefix="/api", tags=["api"])

# Global instances (will be injected)
db_manager: Optional[DatabaseManager] = None
bot_interface: Optional[BotInterface] = None

def set_dependencies(db: DatabaseManager, bot: BotInterface):
    """Set global dependencies"""
    global db_manager, bot_interface
    db_manager = db
    bot_interface = bot

# Guild management routes
@api.get("/guilds/{guild_id}/info")
async def get_guild_info(
    guild_id: int,
    user: User = Depends(get_current_user)
):
    """Get detailed guild information"""
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    try:
        # Get guild info from Discord API
        guild_info = await bot_interface.get_guild_info(guild_id)
        if not guild_info:
            raise HTTPException(status_code=404, detail="Guild not found")
        
        # Get guild settings from database
        settings = await db_manager.get_guild_settings(guild_id)
        
        # Get guild statistics
        stats = await db_manager.get_guild_stats(guild_id)
        
        # Get available commands
        commands = bot_interface.get_all_commands()
        
        return {
            "guild": guild_info,
            "settings": [setting.dict() for setting in settings],
            "commands": [command.dict() for command in commands],
            "stats": stats
        }
        
    except Exception as e:
        logger.error(f"Error getting guild info for {guild_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api.get("/guilds/{guild_id}/settings")
async def get_guild_settings(
    guild_id: int,
    user: User = Depends(get_current_user)
):
    """Get guild settings"""
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    try:
        settings = await db_manager.get_guild_settings(guild_id)
        return [setting.dict() for setting in settings]
    except Exception as e:
        logger.error(f"Error getting guild settings for {guild_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api.put("/guilds/{guild_id}/settings/{setting_key}")
async def update_guild_setting(
    guild_id: int,
    setting_key: str,
    request: SettingUpdateRequest,
    user: User = Depends(get_current_user)
):
    """Update a guild setting"""
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    try:
        success = await db_manager.update_guild_setting(guild_id, setting_key, request.value)
        if success:
            return ApiResponse(success=True, message="Setting updated successfully")
        else:
            raise HTTPException(status_code=400, detail="Failed to update setting")
    except Exception as e:
        logger.error(f"Error updating setting {setting_key} for guild {guild_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Prefix management routes
@api.get("/guilds/{guild_id}/prefixes")
async def get_guild_prefixes(
    guild_id: int,
    user: User = Depends(get_current_user)
):
    """Get guild prefixes"""
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    try:
        prefixes = await db_manager.get_guild_prefixes(guild_id)
        return {"prefixes": prefixes}
    except Exception as e:
        logger.error(f"Error getting prefixes for guild {guild_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api.post("/guilds/{guild_id}/prefixes")
async def add_guild_prefix(
    guild_id: int,
    request: PrefixUpdateRequest,
    user: User = Depends(get_current_user)
):
    """Add a guild prefix"""
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    try:
        for prefix in request.prefixes:
            await db_manager.add_guild_prefix(guild_id, prefix)
        return ApiResponse(success=True, message="Prefixes added successfully")
    except Exception as e:
        logger.error(f"Error adding prefixes for guild {guild_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api.delete("/guilds/{guild_id}/prefixes/{prefix}")
async def remove_guild_prefix(
    guild_id: int,
    prefix: str,
    user: User = Depends(get_current_user)
):
    """Remove a guild prefix"""
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    try:
        success = await db_manager.remove_guild_prefix(guild_id, prefix)
        if success:
            return ApiResponse(success=True, message="Prefix removed successfully")
        else:
            raise HTTPException(status_code=404, detail="Prefix not found")
    except Exception as e:
        logger.error(f"Error removing prefix {prefix} for guild {guild_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Command management routes
@api.get("/commands")
async def get_all_commands():
    """Get all available bot commands"""
    try:
        commands = bot_interface.get_all_commands()
        return [command.dict() for command in commands]
    except Exception as e:
        logger.error(f"Error getting commands: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api.get("/commands/category/{category}")
async def get_commands_by_category(category: CommandCategory):
    """Get commands by category"""
    try:
        commands = bot_interface.get_commands_by_category(category)
        return [command.dict() for command in commands]
    except Exception as e:
        logger.error(f"Error getting commands for category {category}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Moderation routes
@api.get("/guilds/{guild_id}/moderation/actions")
async def get_moderation_actions(
    guild_id: int,
    limit: int = 50,
    user: User = Depends(get_current_user)
):
    """Get recent moderation actions"""
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    try:
        actions = await db_manager.get_moderation_actions(guild_id, limit)
        return [action.dict() for action in actions]
    except Exception as e:
        logger.error(f"Error getting moderation actions for guild {guild_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Statistics routes
@api.get("/guilds/{guild_id}/stats")
async def get_guild_stats(
    guild_id: int,
    user: User = Depends(get_current_user)
):
    """Get guild statistics"""
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    try:
        stats = await db_manager.get_guild_stats(guild_id)
        return stats
    except Exception as e:
        logger.error(f"Error getting stats for guild {guild_id}: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@api.get("/bot/stats")
async def get_bot_stats():
    """Get bot statistics"""
    try:
        stats = await bot_interface.get_bot_stats()
        return stats.dict()
    except Exception as e:
        logger.error(f"Error getting bot stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

# Utility routes
@api.get("/guilds/{guild_id}/channels")
async def get_guild_channels(
    guild_id: int,
    user: User = Depends(get_current_user)
):
    """Get guild channels (for dropdowns)"""
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    # This would typically fetch from Discord API
    # For now, return mock data
    return [
        {"id": "123456789", "name": "general", "type": "text"},
        {"id": "123456790", "name": "announcements", "type": "text"},
        {"id": "123456791", "name": "General Voice", "type": "voice"}
    ]

@api.get("/guilds/{guild_id}/roles")
async def get_guild_roles(
    guild_id: int,
    user: User = Depends(get_current_user)
):
    """Get guild roles (for dropdowns)"""
    if not verify_guild_permissions(user, guild_id):
        raise HTTPException(status_code=403, detail="Access denied to this guild")
    
    # This would typically fetch from Discord API
    # For now, return mock data
    return [
        {"id": "123456789", "name": "@everyone", "color": 0},
        {"id": "123456790", "name": "Admin", "color": 16711680},
        {"id": "123456791", "name": "Moderator", "color": 65280},
        {"id": "123456792", "name": "Member", "color": 255}
    ]

# Health check
@api.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": "2025-01-19T12:00:00Z"}
