# Hina Bot Dashboard

A modern, clean web dashboard for managing your Discord bot with OAuth2 authentication and guild-specific permissions.

## Features

- 🎨 **Modern Dark Theme UI** - Clean, minimal design matching your specifications
- 🔐 **Discord OAuth2 Authentication** - Secure login with Discord
- 🛡️ **Guild Permission System** - Users can only access servers where they have admin permissions
- ⚙️ **Comprehensive Bot Management** - Control all bot features through the web interface
- 📊 **Real-time Statistics** - Monitor bot usage and server activity
- 🎵 **Command Categories** - Admin, Moderation, AutoMod, Music, Utility, and more
- 📱 **Responsive Design** - Works on desktop and mobile devices

## Installation

1. **Install Dependencies**
   ```bash
   cd dashboard
   pip install -r requirements.txt
   ```

2. **Configure Environment**
   - Update Discord OAuth2 credentials in `auth.py`
   - Set JWT secret key for production
   - Ensure database connection string is correct

3. **Start the Dashboard**
   ```bash
   python run.py
   ```

4. **Access the Dashboard**
   - Dashboard: http://localhost:8000
   - API Docs: http://localhost:8000/api/docs
   - Login: http://localhost:8000/auth/login

## Configuration

### Discord OAuth2 Setup

1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application or use existing bot application
3. In OAuth2 settings, add redirect URI: `http://localhost:8000/auth/callback`
4. Update credentials in `auth.py`:
   ```python
   DISCORD_CLIENT_ID = "your_client_id"
   DISCORD_CLIENT_SECRET = "your_client_secret"
   ```

### Database Configuration

The dashboard connects to your existing bot database. Update the connection string in `database.py`:

```python
self.connection_string = "your_postgres_connection_string"
```

## API Endpoints

### Authentication
- `GET /auth/login` - Redirect to Discord OAuth2
- `GET /auth/callback` - Handle OAuth2 callback
- `POST /auth/logout` - Logout user

### Guild Management
- `GET /api/guilds/{guild_id}/info` - Get guild information
- `GET /api/guilds/{guild_id}/settings` - Get guild settings
- `PUT /api/guilds/{guild_id}/settings/{key}` - Update guild setting

### Commands
- `GET /api/commands` - Get all bot commands
- `GET /api/commands/category/{category}` - Get commands by category

### Statistics
- `GET /api/guilds/{guild_id}/stats` - Get guild statistics
- `GET /api/bot/stats` - Get bot statistics

## Security Features

- **JWT Authentication** - Secure session management
- **Guild Permission Verification** - Users can only access servers they admin
- **CORS Protection** - Configured for frontend security
- **Input Validation** - All API inputs are validated
- **Rate Limiting** - Built-in protection against abuse

## Command Categories

The dashboard organizes bot commands into logical categories:

### Admin Commands
- Server prefix management
- Mass moderation actions (dehoist, rename, role management)
- Server lock/unlock functionality
- Guild configuration

### Moderation Commands
- User management (kick, ban, mute)
- Channel management (lock, slowmode)
- Role management
- Temporary actions (tempban, tempmute)

### AutoMod Features
- Warning system
- Automatic invite deletion
- Word filters
- Auto-role assignment
- Sticky roles

### Logging System
- Configurable event logging
- Message snipe functionality
- User search and tracking
- Audit trail

### Music & Utility
- Music bot configuration
- Utility commands
- Server information tools
- User management utilities

## Development

### Project Structure
```
dashboard/
├── app.py              # Main FastAPI application
├── auth.py             # Discord OAuth2 authentication
├── database.py         # Database connection and queries
├── bot_interface.py    # Bot communication interface
├── models.py           # Pydantic data models
├── api_routes.py       # API endpoint definitions
├── templates/          # HTML templates
│   └── index.html      # Main React application
├── static/             # Static assets
├── requirements.txt    # Python dependencies
└── run.py             # Startup script
```

### Adding New Features

1. **Add API Endpoints** - Define new routes in `api_routes.py`
2. **Update Models** - Add data models in `models.py`
3. **Frontend Components** - Add React components in `templates/index.html`
4. **Database Queries** - Add database operations in `database.py`

### Customization

The dashboard is designed to be easily customizable:

- **Theme Colors** - Update CSS variables in the `<style>` section
- **Navigation** - Modify the `menuItems` array in the Sidebar component
- **API Endpoints** - Add new routes following the existing pattern
- **Components** - Create new React components for additional features

## Production Deployment

For production deployment:

1. **Set Environment Variables**
   ```bash
   export JWT_SECRET="your-secure-secret-key"
   export DISCORD_CLIENT_SECRET="your-discord-secret"
   ```

2. **Use Production Server**
   ```bash
   uvicorn app:app --host 0.0.0.0 --port 8000 --workers 4
   ```

3. **Configure Reverse Proxy** (nginx example)
   ```nginx
   location / {
       proxy_pass http://localhost:8000;
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
   }
   ```

4. **SSL Certificate** - Use Let's Encrypt or similar for HTTPS

## Support

For support and questions:
- Check the API documentation at `/api/docs`
- Review the bot's command documentation
- Ensure proper Discord permissions are set

## License

This dashboard is part of the Hina Bot project.
