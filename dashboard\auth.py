"""
Discord OAuth2 Authentication Module
Handles Discord OAuth2 flow and user authentication
"""

import aiohttp
import jwt
import os
from datetime import datetime
from typing import Optional, Dict, Any, List
from fastapi import HTTPException, Request, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import logging

from models import User

logger = logging.getLogger(__name__)

# Discord OAuth2 configuration
DISCORD_CLIENT_ID = "1393143499678814208"
DISCORD_CLIENT_SECRET = "rJMNVmtKRz4CPp0PmKeYY_-DKlXEKhEp"
DISCORD_REDIRECT_URI = "http://localhost:8000/auth/callback"
DISCORD_API_BASE = "https://discord.com/api/v10"

# JWT configuration
JWT_SECRET = os.getenv("JWT_SECRET", "your-secret-key-change-in-production")
JWT_ALGORITHM = "HS256"

security = HTTPBearer(auto_error=False)

class DiscordOAuth:
    """Discord OAuth2 handler"""
    
    def __init__(self):
        self.client_id = DISCORD_CLIENT_ID
        self.client_secret = DISCORD_CLIENT_SECRET
        self.redirect_uri = DISCORD_REDIRECT_URI
        self.api_base = DISCORD_API_BASE
    
    def get_authorization_url(self, scopes: List[str] = None) -> str:
        """Generate Discord OAuth2 authorization URL"""
        if scopes is None:
            scopes = ["identify", "guilds"]
        
        scope_string = " ".join(scopes)
        
        params = {
            "client_id": self.client_id,
            "redirect_uri": self.redirect_uri,
            "response_type": "code",
            "scope": scope_string,
            "prompt": "none"  # Don't force re-authorization
        }
        
        query_string = "&".join([f"{k}={v}" for k, v in params.items()])
        return f"https://discord.com/api/oauth2/authorize?{query_string}"
    
    async def exchange_code_for_token(self, code: str) -> Dict[str, Any]:
        """Exchange authorization code for access token"""
        data = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "grant_type": "authorization_code",
            "code": code,
            "redirect_uri": self.redirect_uri
        }
        
        headers = {
            "Content-Type": "application/x-www-form-urlencoded"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.api_base}/oauth2/token",
                data=data,
                headers=headers
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Token exchange failed: {error_text}")
                    raise HTTPException(
                        status_code=400,
                        detail="Failed to exchange code for token"
                    )
                
                return await response.json()
    
    async def get_user_info(self, access_token: str) -> Dict[str, Any]:
        """Get user information from Discord API"""
        headers = {
            "Authorization": f"Bearer {access_token}"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.api_base}/users/@me",
                headers=headers
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Failed to get user info: {error_text}")
                    raise HTTPException(
                        status_code=400,
                        detail="Failed to get user information"
                    )
                
                return await response.json()
    
    async def get_user_guilds(self, access_token: str) -> List[Dict[str, Any]]:
        """Get user's guilds from Discord API"""
        headers = {
            "Authorization": f"Bearer {access_token}"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                f"{self.api_base}/users/@me/guilds",
                headers=headers
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Failed to get user guilds: {error_text}")
                    raise HTTPException(
                        status_code=400,
                        detail="Failed to get user guilds"
                    )
                
                return await response.json()

def decode_jwt_token(token: str) -> Dict[str, Any]:
    """Decode and validate JWT token"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired"
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

async def get_current_user(request: Request) -> User:
    """Get current authenticated user from request"""
    # Try to get token from cookie first
    token = request.cookies.get("auth_token")
    
    # If no cookie, try Authorization header
    if not token:
        credentials: HTTPAuthorizationCredentials = await security(request)
        if credentials:
            token = credentials.credentials
    
    if not token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    
    # Decode and validate token
    payload = decode_jwt_token(token)
    
    # Create User object from payload
    user = User(
        id=payload["user_id"],
        username=payload["username"],
        discriminator=payload["discriminator"],
        avatar=payload["avatar"],
        admin_guilds=payload["admin_guilds"]
    )
    
    return user

def verify_guild_permissions(user: User, guild_id: int) -> bool:
    """Verify user has admin permissions in the specified guild"""
    for guild in user.admin_guilds:
        if int(guild["id"]) == guild_id:
            # Check if user has administrator permission (0x8)
            return (int(guild["permissions"]) & 0x8) == 0x8

    return False

async def get_optional_user(request: Request) -> Optional[User]:
    """Get current user if authenticated, otherwise return None"""
    try:
        return await get_current_user(request)
    except HTTPException:
        return None
