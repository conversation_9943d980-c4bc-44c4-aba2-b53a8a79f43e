"""
Bot interface module for communicating with the Discord bot
"""

import asyncio
import aiohttp
import json
import logging
from typing import Optional, Dict, Any, List
import discord
from discord.ext import commands

from models import Guild, Command, CommandCategory, BotStats

logger = logging.getLogger(__name__)

class BotInterface:
    """Interface for communicating with the Discord bot"""
    
    def __init__(self):
        self.bot_token = "MTM5MjIwNjI2MDYwMTg4NDc2Mw.G21iOb.9udfqb15pd76_AjMPNoDeU1r7ayoWllPimrsl8"
        self.api_base = "https://discord.com/api/v10"
        self.bot_instance = None
        
        # Command definitions based on cog analysis
        self.commands = self._initialize_commands()
    
    async def initialize(self):
        """Initialize bot interface"""
        logger.info("Bot interface initialized")
    
    async def close(self):
        """Close bot interface"""
        logger.info("Bot interface closed")
    
    def _initialize_commands(self) -> List[Command]:
        """Initialize command definitions from bot analysis"""
        commands = []
        
        # Admin commands
        admin_commands = [
            Command(
                name="muterole",
                category=CommandCategory.ADMIN,
                description="Setup server muting system",
                aliases=["setmuterole"],
                permissions_required=["manage_guild"],
                bot_permissions_required=["manage_roles"]
            ),
            Command(
                name="leave",
                category=CommandCategory.ADMIN,
                description="Have the bot leave the server",
                aliases=["die"],
                permissions_required=["manage_guild"]
            ),
            Command(
                name="massdehoist",
                category=CommandCategory.ADMIN,
                description="Dehoist all server users",
                permissions_required=["manage_guild"],
                bot_permissions_required=["manage_nicknames"]
            ),
            Command(
                name="massascify",
                category=CommandCategory.ADMIN,
                description="Mass nickname users with odd names",
                permissions_required=["manage_guild"],
                bot_permissions_required=["manage_nicknames"]
            ),
            Command(
                name="massrenick",
                category=CommandCategory.ADMIN,
                description="Reset all server nicknames",
                aliases=["massrenickname"],
                permissions_required=["manage_guild"],
                bot_permissions_required=["manage_nicknames"]
            ),
            Command(
                name="massban",
                category=CommandCategory.ADMIN,
                description="Massban users matching a search",
                aliases=["multiban"],
                permissions_required=["ban_members"],
                bot_permissions_required=["ban_members"]
            ),
            Command(
                name="masskick",
                category=CommandCategory.ADMIN,
                description="Mass kick users matching a search",
                aliases=["multikick"],
                permissions_required=["kick_members"],
                bot_permissions_required=["kick_members"]
            ),
            Command(
                name="massrole",
                category=CommandCategory.ADMIN,
                description="Manage mass adding/removing roles",
                aliases=["multirole"],
                permissions_required=["manage_roles"],
                bot_permissions_required=["manage_roles"]
            ),
            Command(
                name="prefix",
                category=CommandCategory.ADMIN,
                description="Manage server prefixes",
                permissions_required=["manage_guild"]
            ),
            Command(
                name="freeze",
                category=CommandCategory.ADMIN,
                description="Lock all server channels",
                aliases=["serverlock", "lockserver", "frost"],
                permissions_required=["manage_channels"],
                bot_permissions_required=["manage_channels"]
            ),
            Command(
                name="unfreeze",
                category=CommandCategory.ADMIN,
                description="Unlock all server channels",
                aliases=["unlockserver", "serverunlock", "melt", "defrost"],
                permissions_required=["manage_channels"],
                bot_permissions_required=["manage_channels"]
            )
        ]
        commands.extend(admin_commands)
        
        # Moderation commands
        mod_commands = [
            Command(
                name="block",
                category=CommandCategory.MODERATION,
                description="Restrict users from sending messages",
                permissions_required=["manage_messages"],
                bot_permissions_required=["manage_channels"]
            ),
            Command(
                name="unblock",
                category=CommandCategory.MODERATION,
                description="Reallow users to send messages",
                permissions_required=["manage_messages"],
                bot_permissions_required=["manage_channels"]
            ),
            Command(
                name="blind",
                category=CommandCategory.MODERATION,
                description="Hide a channel from a user",
                permissions_required=["manage_channels"],
                bot_permissions_required=["manage_channels"]
            ),
            Command(
                name="unblind",
                category=CommandCategory.MODERATION,
                description="Reallow users see a channel",
                permissions_required=["manage_channels"],
                bot_permissions_required=["manage_channels"]
            ),
            Command(
                name="kick",
                category=CommandCategory.MODERATION,
                description="Kick users from the server",
                permissions_required=["kick_members"],
                bot_permissions_required=["kick_members"]
            ),
            Command(
                name="ban",
                category=CommandCategory.MODERATION,
                description="Ban users from the server",
                permissions_required=["ban_members"],
                bot_permissions_required=["ban_members"]
            ),
            Command(
                name="softban",
                category=CommandCategory.MODERATION,
                description="Softban users from the server",
                permissions_required=["ban_members"],
                bot_permissions_required=["ban_members"]
            ),
            Command(
                name="unban",
                category=CommandCategory.MODERATION,
                description="Unban a previously banned user",
                aliases=["revokeban"],
                permissions_required=["ban_members"],
                bot_permissions_required=["ban_members"]
            ),
            Command(
                name="slowmode",
                category=CommandCategory.MODERATION,
                description="Set the slowmode for a channel",
                permissions_required=["manage_channels"],
                bot_permissions_required=["manage_channels"]
            ),
            Command(
                name="lock",
                category=CommandCategory.MODERATION,
                description="Prevent messages in a channel",
                aliases=["lockdown", "lockchannel"],
                permissions_required=["manage_channels"],
                bot_permissions_required=["manage_channels"]
            ),
            Command(
                name="unlock",
                category=CommandCategory.MODERATION,
                description="Unlock a channel",
                aliases=["unlockchannel", "unlockdown"],
                permissions_required=["manage_channels"],
                bot_permissions_required=["manage_channels"]
            ),
            Command(
                name="tempban",
                category=CommandCategory.MODERATION,
                description="Temporarily ban users",
                aliases=["tban"],
                permissions_required=["ban_members"],
                bot_permissions_required=["ban_members"]
            ),
            Command(
                name="mute",
                category=CommandCategory.MODERATION,
                description="Mute users for a duration",
                aliases=["tempmute"],
                permissions_required=["manage_roles"],
                bot_permissions_required=["manage_roles"]
            ),
            Command(
                name="unmute",
                category=CommandCategory.MODERATION,
                description="Unmute muted users",
                permissions_required=["manage_roles"],
                bot_permissions_required=["manage_roles"]
            ),
            Command(
                name="temprole",
                category=CommandCategory.MODERATION,
                description="Temporarily add roles to users",
                aliases=["trole"],
                permissions_required=["manage_roles"],
                bot_permissions_required=["manage_roles"]
            ),
            Command(
                name="addrole",
                category=CommandCategory.MODERATION,
                description="Add multiple roles to a user",
                aliases=["ar", "addroles"],
                permissions_required=["manage_roles"],
                bot_permissions_required=["manage_roles"]
            ),
            Command(
                name="removerole",
                category=CommandCategory.MODERATION,
                description="Remove multiple roles from a user",
                aliases=["rr", "rmrole", "remrole"],
                permissions_required=["manage_roles"],
                bot_permissions_required=["manage_roles"]
            )
        ]
        commands.extend(mod_commands)
        
        # Automod commands
        automod_commands = [
            Command(
                name="warn",
                category=CommandCategory.AUTOMOD,
                description="Warn users with an optional reason",
                aliases=["strike"],
                permissions_required=["manage_messages"]
            ),
            Command(
                name="warnings",
                category=CommandCategory.AUTOMOD,
                description="Count the warnings a user has",
                permissions_required=["manage_messages"]
            ),
            Command(
                name="listwarns",
                category=CommandCategory.AUTOMOD,
                description="Show all warnings a user has",
                permissions_required=["manage_messages"]
            ),
            Command(
                name="clearwarns",
                category=CommandCategory.AUTOMOD,
                description="Clear warnings from a user",
                aliases=["deletewarnings", "removewarns"],
                permissions_required=["manage_messages"]
            ),
            Command(
                name="delwarn",
                category=CommandCategory.AUTOMOD,
                description="Revoke a warning from a user",
                aliases=["unstrike"],
                permissions_required=["manage_messages"]
            ),
            Command(
                name="warnlist",
                category=CommandCategory.AUTOMOD,
                description="Display the server warnlist",
                aliases=["serverwarns"],
                permissions_required=["manage_messages"]
            ),
            Command(
                name="antiinvite",
                category=CommandCategory.AUTOMOD,
                description="Enable or disable auto-deleting invite links",
                aliases=["removeinvitelinks", "deleteinvites", "antiinvites"],
                permissions_required=["manage_guild"]
            ),
            Command(
                name="autorole",
                category=CommandCategory.AUTOMOD,
                description="Assign roles to new members",
                aliases=["autoroles", "autoassign"],
                permissions_required=["manage_roles"],
                bot_permissions_required=["manage_roles"]
            ),
            Command(
                name="sticky",
                category=CommandCategory.AUTOMOD,
                description="Reassign roles on user rejoin",
                aliases=["stickyroles"],
                permissions_required=["manage_roles"],
                bot_permissions_required=["manage_roles"]
            ),
            Command(
                name="filter",
                category=CommandCategory.AUTOMOD,
                description="Manage word filters",
                permissions_required=["manage_messages"]
            )
        ]
        commands.extend(automod_commands)
        
        return commands
    
    async def get_guild_info(self, guild_id: int) -> Optional[Dict[str, Any]]:
        """Get guild information from Discord API"""
        headers = {
            "Authorization": f"Bot {self.bot_token}",
            "Content-Type": "application/json"
        }
        
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(
                    f"{self.api_base}/guilds/{guild_id}",
                    headers=headers
                ) as response:
                    if response.status == 200:
                        guild_data = await response.json()
                        return {
                            "id": guild_data["id"],
                            "name": guild_data["name"],
                            "icon": guild_data.get("icon"),
                            "member_count": guild_data.get("approximate_member_count", 0),
                            "features": guild_data.get("features", [])
                        }
                    else:
                        logger.error(f"Failed to get guild {guild_id}: {response.status}")
                        return None
            except Exception as e:
                logger.error(f"Error getting guild info for {guild_id}: {e}")
                return None
    
    async def get_bot_stats(self) -> BotStats:
        """Get bot statistics"""
        # This would typically connect to the bot instance
        # For now, return mock data
        return BotStats(
            guild_count=150,
            user_count=50000,
            command_count=1000000,
            uptime="7 days, 12 hours",
            latency=45.2,
            memory_usage=256.7,
            cpu_usage=12.5
        )
    
    def get_commands_by_category(self, category: CommandCategory) -> List[Command]:
        """Get commands filtered by category"""
        return [cmd for cmd in self.commands if cmd.category == category]
    
    def get_all_commands(self) -> List[Command]:
        """Get all available commands"""
        return self.commands
