"""
Database manager for the dashboard
Handles connections to the bot's PostgreSQL database
"""

import asyncpg
import json
import logging
from typing import Optional, List, Dict, Any, Union
from datetime import datetime

from models import Setting, Command, Guild, ModerationAction, LogEvent

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Manages database connections and operations"""
    
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.connection_string = "postgres://avnadmin:<EMAIL>:20384/defaultdb?sslmode=require"
    
    async def initialize(self):
        """Initialize database connection pool"""
        try:
            self.pool = await asyncpg.create_pool(
                self.connection_string,
                min_size=1,
                max_size=10,
                command_timeout=60
            )
            logger.info("Database connection pool initialized")
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    async def close(self):
        """Close database connection pool"""
        if self.pool:
            await self.pool.close()
            logger.info("Database connection pool closed")
    
    async def get_guild_settings(self, guild_id: int) -> List[Setting]:
        """Get all settings for a guild"""
        async with self.pool.acquire() as conn:
            # Get guild configuration - using the correct table structure
            query = """
                SELECT * FROM servers
                WHERE server_id = $1
            """
            config_row = await conn.fetchrow(query, guild_id)
            
            # Get logging configuration
            log_query = """
                SELECT * FROM logging 
                WHERE server_id = $1
            """
            log_row = await conn.fetchrow(query, guild_id)
            
            # Get automod configuration
            automod_query = """
                SELECT * FROM automod 
                WHERE server_id = $1
            """
            automod_row = await conn.fetchrow(automod_query, guild_id)
            
            # Convert to Setting objects
            settings = []
            
            # Basic guild settings
            if config_row:
                settings.extend([
                    Setting(
                        key="prefix",
                        name="Command Prefix",
                        description="Custom command prefix for this server",
                        category="general",
                        type="string",
                        value=config_row.get("prefix"),
                        default_value="-"
                    ),
                    Setting(
                        key="mute_role",
                        name="Mute Role",
                        description="Role assigned to muted users",
                        category="moderation",
                        type="role",
                        value=str(config_row.get("mute_role")) if config_row.get("mute_role") else None
                    )
                ])
            
            # Logging settings
            if log_row:
                settings.extend([
                    Setting(
                        key="log_channel",
                        name="Log Channel",
                        description="Channel for server logs",
                        category="logging",
                        type="channel",
                        value=str(log_row.get("channel")) if log_row.get("channel") else None
                    ),
                    Setting(
                        key="log_joins",
                        name="Log Member Joins",
                        description="Log when members join the server",
                        category="logging",
                        type="boolean",
                        value=log_row.get("joins", False)
                    ),
                    Setting(
                        key="log_leaves",
                        name="Log Member Leaves",
                        description="Log when members leave the server",
                        category="logging",
                        type="boolean",
                        value=log_row.get("leaves", False)
                    )
                ])
            
            return settings
    
    async def update_guild_setting(self, guild_id: int, key: str, value: Any) -> bool:
        """Update a guild setting"""
        async with self.pool.acquire() as conn:
            try:
                if key == "prefix":
                    query = """
                        INSERT INTO config (server_id, prefix)
                        VALUES ($1, $2)
                        ON CONFLICT (server_id)
                        DO UPDATE SET prefix = $2
                    """
                    await conn.execute(query, guild_id, value)
                
                elif key == "mute_role":
                    query = """
                        INSERT INTO config (server_id, mute_role)
                        VALUES ($1, $2)
                        ON CONFLICT (server_id)
                        DO UPDATE SET mute_role = $2
                    """
                    await conn.execute(query, guild_id, int(value) if value else None)
                
                elif key == "log_channel":
                    query = """
                        INSERT INTO logging (server_id, channel)
                        VALUES ($1, $2)
                        ON CONFLICT (server_id)
                        DO UPDATE SET channel = $2
                    """
                    await conn.execute(query, guild_id, int(value) if value else None)
                
                elif key.startswith("log_"):
                    event_type = key[4:]  # Remove "log_" prefix
                    query = f"""
                        INSERT INTO logging (server_id, {event_type})
                        VALUES ($1, $2)
                        ON CONFLICT (server_id)
                        DO UPDATE SET {event_type} = $2
                    """
                    await conn.execute(query, guild_id, bool(value))
                
                return True
                
            except Exception as e:
                logger.error(f"Failed to update setting {key} for guild {guild_id}: {e}")
                return False
    
    async def get_guild_prefixes(self, guild_id: int) -> List[str]:
        """Get custom prefixes for a guild"""
        async with self.pool.acquire() as conn:
            query = """
                SELECT prefix FROM prefixes 
                WHERE server_id = $1
                ORDER BY prefix
            """
            rows = await conn.fetch(query, guild_id)
            return [row["prefix"] for row in rows]
    
    async def add_guild_prefix(self, guild_id: int, prefix: str) -> bool:
        """Add a custom prefix for a guild"""
        async with self.pool.acquire() as conn:
            try:
                query = """
                    INSERT INTO prefixes (server_id, prefix)
                    VALUES ($1, $2)
                    ON CONFLICT (server_id, prefix) DO NOTHING
                """
                await conn.execute(query, guild_id, prefix)
                return True
            except Exception as e:
                logger.error(f"Failed to add prefix {prefix} for guild {guild_id}: {e}")
                return False
    
    async def remove_guild_prefix(self, guild_id: int, prefix: str) -> bool:
        """Remove a custom prefix for a guild"""
        async with self.pool.acquire() as conn:
            try:
                query = """
                    DELETE FROM prefixes 
                    WHERE server_id = $1 AND prefix = $2
                """
                result = await conn.execute(query, guild_id, prefix)
                return result != "DELETE 0"
            except Exception as e:
                logger.error(f"Failed to remove prefix {prefix} for guild {guild_id}: {e}")
                return False
    
    async def get_moderation_actions(self, guild_id: int, limit: int = 50) -> List[ModerationAction]:
        """Get recent moderation actions for a guild"""
        async with self.pool.acquire() as conn:
            query = """
                SELECT * FROM moderation_log 
                WHERE server_id = $1
                ORDER BY created_at DESC
                LIMIT $2
            """
            rows = await conn.fetch(query, guild_id, limit)
            
            actions = []
            for row in rows:
                action = ModerationAction(
                    id=str(row["id"]),
                    type=row["action_type"],
                    user_id=str(row["user_id"]),
                    moderator_id=str(row["moderator_id"]),
                    reason=row.get("reason"),
                    duration=row.get("duration"),
                    created_at=row["created_at"],
                    expires_at=row.get("expires_at")
                )
                actions.append(action)
            
            return actions
    
    async def get_guild_stats(self, guild_id: int) -> Dict[str, Any]:
        """Get statistics for a guild"""
        async with self.pool.acquire() as conn:
            try:
                # Try to get command usage stats (create table if not exists)
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS command_usage (
                        id BIGSERIAL PRIMARY KEY,
                        server_id BIGINT,
                        user_id BIGINT,
                        command TEXT,
                        used_at TIMESTAMP DEFAULT NOW()
                    )
                """)

                cmd_query = """
                    SELECT command, COUNT(*) as usage_count
                    FROM command_usage
                    WHERE server_id = $1
                    AND used_at > NOW() - INTERVAL '30 days'
                    GROUP BY command
                    ORDER BY usage_count DESC
                    LIMIT 10
                """
                cmd_stats = await conn.fetch(cmd_query, guild_id)

                # Try to get moderation stats (create table if not exists)
                await conn.execute("""
                    CREATE TABLE IF NOT EXISTS moderation_log (
                        id BIGSERIAL PRIMARY KEY,
                        server_id BIGINT,
                        user_id BIGINT,
                        moderator_id BIGINT,
                        action_type TEXT,
                        reason TEXT,
                        duration INTEGER,
                        created_at TIMESTAMP DEFAULT NOW(),
                        expires_at TIMESTAMP
                    )
                """)

                mod_query = """
                    SELECT action_type, COUNT(*) as count
                    FROM moderation_log
                    WHERE server_id = $1
                    AND created_at > NOW() - INTERVAL '30 days'
                    GROUP BY action_type
                """
                mod_stats = await conn.fetch(mod_query, guild_id)

                return {
                    "command_usage": [dict(row) for row in cmd_stats],
                    "moderation_actions": [dict(row) for row in mod_stats],
                    "total_commands": sum(row["usage_count"] for row in cmd_stats) if cmd_stats else 0,
                    "total_mod_actions": sum(row["count"] for row in mod_stats) if mod_stats else 0
                }
            except Exception as e:
                logger.error(f"Error getting guild stats: {e}")
                # Return default stats if database queries fail
                return {
                    "command_usage": [],
                    "moderation_actions": [],
                    "total_commands": 0,
                    "total_mod_actions": 0
                }
