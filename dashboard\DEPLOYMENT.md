# Hina Bot Dashboard - Deployment Guide

## 🚀 Complete Feature List

### ✅ **Implemented Features:**

#### **Authentication & Security**
- ✅ Discord OAuth2 login with proper scopes
- ✅ JWT token-based session management
- ✅ Guild permission verification (admin-only access)
- ✅ Secure cookie handling
- ✅ CORS protection

#### **Real-time Features**
- ✅ WebSocket integration for live updates
- ✅ Connection status monitoring
- ✅ Real-time notifications
- ✅ Auto-reconnection on disconnect

#### **Dashboard Pages**
- ✅ **Dashboard**: Statistics, quick actions, recent activity
- ✅ **Admin**: Prefix management, mass actions, server controls
- ✅ **Moderation**: User management, action history, mod settings
- ✅ **AutoMod**: Warning system, auto roles, word filters, protection
- ✅ **Logging**: Event logging configuration, log channel setup
- ✅ **Music**: DJ settings, playback configuration, command reference
- ✅ **Utility**: Command reference organized by category
- ✅ **Settings**: Bot configuration, appearance, general settings

#### **Command Management**
- ✅ Complete command interface for all categories
- ✅ Form validation and error handling
- ✅ Real-time setting updates
- ✅ Interactive toggles and controls

#### **Database Integration**
- ✅ PostgreSQL connection with connection pooling
- ✅ Automatic table creation for missing tables
- ✅ Settings management with fallback defaults
- ✅ Statistics tracking and display

## 🔧 **Installation & Setup**

### **1. Install Dependencies**
```bash
cd dashboard
pip install -r requirements.txt
```

### **2. Configure Discord OAuth2**
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Select your bot application
3. Navigate to OAuth2 → General
4. Add redirect URL: `http://localhost:8000/auth/callback`
5. Update credentials in `auth.py` if needed

### **3. Environment Variables**
```bash
# Set these for production
export JWT_SECRET="your-super-secure-secret-key"
export DISCORD_CLIENT_SECRET="your-discord-client-secret"
```

### **4. Start the Dashboard**
```bash
python run.py
```

## 🌐 **Production Deployment**

### **Option 1: Docker Deployment**
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["uvicorn", "app:app", "--host", "0.0.0.0", "--port", "8000"]
```

### **Option 2: Systemd Service**
```ini
[Unit]
Description=Hina Bot Dashboard
After=network.target

[Service]
Type=simple
User=hina
WorkingDirectory=/opt/hina-dashboard
Environment=JWT_SECRET=your-secret-key
ExecStart=/usr/bin/python3 -m uvicorn app:app --host 0.0.0.0 --port 8000
Restart=always

[Install]
WantedBy=multi-user.target
```

### **Option 3: Nginx Reverse Proxy**
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /ws/ {
        proxy_pass http://localhost:8000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
}
```

## 🔒 **Security Best Practices**

### **Production Security Checklist**
- [ ] Change JWT secret key to a strong, random value
- [ ] Use HTTPS in production (update redirect URIs)
- [ ] Set secure cookie flags (`secure=True`)
- [ ] Configure proper CORS origins
- [ ] Use environment variables for secrets
- [ ] Enable database SSL connections
- [ ] Set up proper firewall rules
- [ ] Regular security updates

### **Environment Variables for Production**
```bash
JWT_SECRET="your-256-bit-secret-key"
DISCORD_CLIENT_SECRET="your-discord-client-secret"
DATABASE_URL="postgresql://user:pass@host:port/db?sslmode=require"
ENVIRONMENT="production"
```

## 📊 **Monitoring & Logging**

### **Health Check Endpoint**
```bash
curl http://localhost:8000/api/health
```

### **Log Monitoring**
The dashboard provides detailed logging for:
- Authentication events
- Database operations
- WebSocket connections
- API requests
- Error tracking

### **Performance Monitoring**
- Connection pool status
- WebSocket connection count
- API response times
- Database query performance

## 🔄 **Backup & Recovery**

### **Database Backup**
```bash
pg_dump $DATABASE_URL > hina_dashboard_backup.sql
```

### **Configuration Backup**
- Export guild settings
- Backup user permissions
- Save custom configurations

## 🚨 **Troubleshooting**

### **Common Issues**

1. **OAuth Callback Error**
   - Check redirect URI matches exactly
   - Verify Discord application credentials
   - Ensure bot has proper permissions

2. **Database Connection Issues**
   - Verify connection string
   - Check database permissions
   - Ensure tables exist (auto-created on startup)

3. **WebSocket Connection Failed**
   - Check firewall settings
   - Verify WebSocket support in proxy
   - Check browser console for errors

4. **Permission Denied**
   - Verify user has admin permissions in guild
   - Check Discord role hierarchy
   - Ensure bot is in the server

### **Debug Mode**
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG
python run.py
```

## 📈 **Performance Optimization**

### **Database Optimization**
- Connection pooling (already implemented)
- Query optimization
- Index creation for frequently accessed data

### **Frontend Optimization**
- Component memoization
- Lazy loading for large datasets
- Efficient WebSocket message handling

### **Caching Strategy**
- Redis for session storage
- Database query caching
- Static asset caching

## 🔮 **Future Enhancements**

### **Planned Features**
- [ ] Advanced analytics dashboard
- [ ] Custom command builder
- [ ] Plugin system for extensions
- [ ] Mobile app companion
- [ ] Advanced role management
- [ ] Scheduled actions
- [ ] Backup/restore functionality
- [ ] Multi-language support

### **API Extensions**
- [ ] Webhook integrations
- [ ] Third-party service connections
- [ ] Advanced automation rules
- [ ] Custom dashboard themes

## 📞 **Support**

For issues and support:
1. Check the troubleshooting section
2. Review logs for error details
3. Verify configuration settings
4. Test with minimal setup

## 🎉 **Success Metrics**

Your dashboard is successfully deployed when:
- ✅ Users can log in with Discord OAuth2
- ✅ Guild selection works correctly
- ✅ All pages load without errors
- ✅ Settings can be updated and saved
- ✅ WebSocket connection is stable
- ✅ Real-time updates work properly
- ✅ Database operations complete successfully
