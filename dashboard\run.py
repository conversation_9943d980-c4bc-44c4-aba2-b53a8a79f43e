#!/usr/bin/env python3
"""
Hina Bot Dashboard Startup Script
"""

import uvicorn
import os
import sys
from pathlib import Path

# Add the dashboard directory to Python path
dashboard_dir = Path(__file__).parent
sys.path.insert(0, str(dashboard_dir))

def main():
    """Start the dashboard server"""
    print("🚀 Starting Hina Bot Dashboard...")
    print("📊 Dashboard will be available at: http://localhost:8000")
    print("📚 API documentation at: http://localhost:8000/api/docs")
    print("🔐 Login at: http://localhost:8000/auth/login")
    print()
    
    # Set environment variables
    os.environ.setdefault("JWT_SECRET", "your-secret-key-change-in-production")
    
    try:
        uvicorn.run(
            "app:app",
            host="0.0.0.0",
            port=8000,
            reload=True,
            reload_dirs=[str(dashboard_dir)],
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n👋 Dashboard stopped by user")
    except Exception as e:
        print(f"❌ Failed to start dashboard: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
