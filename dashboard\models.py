"""
Data models for the dashboard application
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum

class User(BaseModel):
    """Discord user model"""
    id: str
    username: str
    discriminator: str
    avatar: Optional[str] = None
    admin_guilds: List[Dict[str, Any]] = []
    
    @property
    def avatar_url(self) -> str:
        """Get user's avatar URL"""
        if self.avatar:
            return f"https://cdn.discordapp.com/avatars/{self.id}/{self.avatar}.png"
        else:
            # Default avatar
            discriminator_int = int(self.discriminator) if self.discriminator != "0" else 0
            return f"https://cdn.discordapp.com/embed/avatars/{discriminator_int % 5}.png"
    
    @property
    def display_name(self) -> str:
        """Get user's display name"""
        if self.discriminator == "0":
            return self.username
        return f"{self.username}#{self.discriminator}"

class Guild(BaseModel):
    """Discord guild/server model"""
    id: str
    name: str
    icon: Optional[str] = None
    owner: bool = False
    permissions: int = 0
    features: List[str] = []
    member_count: Optional[int] = None
    
    @property
    def icon_url(self) -> Optional[str]:
        """Get guild's icon URL"""
        if self.icon:
            return f"https://cdn.discordapp.com/icons/{self.id}/{self.icon}.png"
        return None

class CommandCategory(str, Enum):
    """Command categories"""
    ADMIN = "admin"
    MODERATION = "moderation"
    AUTOMOD = "automod"
    MUSIC = "music"
    UTILITY = "utility"
    INFO = "info"
    LOGGING = "logging"
    SPOTIFY = "spotify"
    CONFIG = "config"

class Command(BaseModel):
    """Bot command model"""
    name: str
    category: CommandCategory
    description: str
    aliases: List[str] = []
    usage: Optional[str] = None
    examples: List[str] = []
    permissions_required: List[str] = []
    bot_permissions_required: List[str] = []
    enabled: bool = True
    cooldown: Optional[int] = None

class SettingType(str, Enum):
    """Setting value types"""
    STRING = "string"
    INTEGER = "integer"
    BOOLEAN = "boolean"
    CHANNEL = "channel"
    ROLE = "role"
    USER = "user"
    COLOR = "color"
    JSON = "json"

class Setting(BaseModel):
    """Guild setting model"""
    key: str
    name: str
    description: str
    category: str
    type: SettingType
    value: Optional[Union[str, int, bool, Dict[str, Any]]] = None
    default_value: Optional[Union[str, int, bool, Dict[str, Any]]] = None
    required: bool = False
    options: Optional[List[str]] = None  # For dropdown/select settings

class LogEvent(BaseModel):
    """Logging event model"""
    event_type: str
    enabled: bool
    channel_id: Optional[str] = None
    webhook_url: Optional[str] = None

class AutomodRule(BaseModel):
    """Automod rule model"""
    name: str
    enabled: bool
    trigger_type: str
    action: str
    settings: Dict[str, Any] = {}

class ModerationAction(BaseModel):
    """Moderation action model"""
    id: str
    type: str  # ban, kick, mute, warn, etc.
    user_id: str
    moderator_id: str
    reason: Optional[str] = None
    duration: Optional[int] = None  # For temporary actions
    created_at: datetime
    expires_at: Optional[datetime] = None

class BotStats(BaseModel):
    """Bot statistics model"""
    guild_count: int
    user_count: int
    command_count: int
    uptime: str
    latency: float
    memory_usage: float
    cpu_usage: float

class DashboardConfig(BaseModel):
    """Dashboard configuration"""
    theme: str = "dark"
    sidebar_collapsed: bool = False
    notifications_enabled: bool = True
    auto_refresh: bool = True
    refresh_interval: int = 30  # seconds

# Request/Response models
class CommandExecuteRequest(BaseModel):
    """Request to execute a bot command"""
    command: str
    args: List[str] = []
    channel_id: Optional[str] = None

class SettingUpdateRequest(BaseModel):
    """Request to update a guild setting"""
    key: str
    value: Union[str, int, bool, Dict[str, Any]]

class LoggingConfigRequest(BaseModel):
    """Request to update logging configuration"""
    events: List[LogEvent]
    channel_id: Optional[str] = None

class AutomodConfigRequest(BaseModel):
    """Request to update automod configuration"""
    rules: List[AutomodRule]

class PrefixUpdateRequest(BaseModel):
    """Request to update guild prefixes"""
    prefixes: List[str]

class MuteRoleRequest(BaseModel):
    """Request to set mute role"""
    role_id: str

class WelcomeMessageRequest(BaseModel):
    """Request to update welcome message"""
    enabled: bool
    channel_id: Optional[str] = None
    message: Optional[str] = None
    embed: Optional[Dict[str, Any]] = None

# Response models
class ApiResponse(BaseModel):
    """Generic API response"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

class CommandResponse(BaseModel):
    """Bot command execution response"""
    success: bool
    output: str
    error: Optional[str] = None

class GuildInfo(BaseModel):
    """Detailed guild information"""
    guild: Guild
    settings: List[Setting]
    commands: List[Command]
    stats: Dict[str, Any]
    bot_permissions: List[str]
    
class UserPermissions(BaseModel):
    """User permissions in a guild"""
    is_admin: bool
    is_moderator: bool
    can_manage_guild: bool
    can_manage_channels: bool
    can_manage_roles: bool
    can_ban_members: bool
    can_kick_members: bool
