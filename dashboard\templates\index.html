<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>na <PERSON> Dashboard</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Babel for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS for the dark theme -->
    <style>
        :root {
            --bg-primary: #1a1a2e;
            --bg-secondary: #16213e;
            --bg-tertiary: #0f172a;
            --accent-purple: #8b5cf6;
            --accent-cyan: #06b6d4;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border-color: #334155;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
        }
        
        body {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
            border-right: 1px solid var(--border-color);
        }
        
        .nav-item {
            transition: all 0.2s ease;
            border-radius: 8px;
            margin: 4px 0;
        }
        
        .nav-item:hover {
            background: rgba(139, 92, 246, 0.1);
            transform: translateX(4px);
        }
        
        .nav-item.active {
            background: linear-gradient(90deg, var(--accent-purple) 0%, var(--accent-cyan) 100%);
            color: white;
        }
        
        .button-primary {
            background: linear-gradient(90deg, var(--accent-purple) 0%, var(--accent-cyan) 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .button-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
        }
        
        .button-secondary {
            background: transparent;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 24px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .button-secondary:hover {
            border-color: var(--accent-purple);
            color: var(--accent-purple);
        }
        
        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 24px;
            margin: 16px 0;
        }
        
        .input-field {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            color: var(--text-primary);
            width: 100%;
            transition: all 0.2s ease;
        }
        
        .input-field:focus {
            outline: none;
            border-color: var(--accent-purple);
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }
        
        .toggle-switch {
            position: relative;
            width: 48px;
            height: 24px;
            background: var(--bg-tertiary);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .toggle-switch.active {
            background: var(--accent-purple);
        }
        
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.2s ease;
        }
        
        .toggle-switch.active::after {
            transform: translateX(24px);
        }
        
        .dropdown {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
        }
        
        .dropdown option {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }
        
        .status-online {
            color: var(--success);
        }
        
        .status-offline {
            color: var(--error);
        }
        
        .status-idle {
            color: var(--warning);
        }
        
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: var(--accent-purple);
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'bg-primary': '#1a1a2e',
                        'bg-secondary': '#16213e',
                        'bg-tertiary': '#0f172a',
                        'accent-purple': '#8b5cf6',
                        'accent-cyan': '#06b6d4',
                        'text-primary': '#f8fafc',
                        'text-secondary': '#cbd5e1',
                        'text-muted': '#64748b',
                        'border-color': '#334155'
                    }
                }
            }
        }
    </script>
</head>
<body>
    <div id="root"></div>
    
    <!-- Main React Application -->
    <script type="text/babel">
        const { useState, useEffect, useCallback } = React;

        // WebSocket connection for real-time updates
        let websocket = null;

        // Main App Component
        function App() {
            const [user, setUser] = useState(null);
            const [selectedGuild, setSelectedGuild] = useState(null);
            const [currentPage, setCurrentPage] = useState('dashboard');
            const [loading, setLoading] = useState(true);
            const [connectionStatus, setConnectionStatus] = useState('disconnected');

            useEffect(() => {
                checkAuth();
            }, []);

            useEffect(() => {
                if (selectedGuild && user) {
                    connectWebSocket();
                }
                return () => {
                    if (websocket) {
                        websocket.close();
                    }
                };
            }, [selectedGuild]);

            const connectWebSocket = () => {
                if (websocket) {
                    websocket.close();
                }

                const wsUrl = `ws://localhost:8000/ws/${selectedGuild.id}`;
                websocket = new WebSocket(wsUrl);

                websocket.onopen = () => {
                    console.log('WebSocket connected');
                    setConnectionStatus('connected');
                };

                websocket.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    console.log('WebSocket message:', data);

                    // Handle different types of real-time updates
                    if (data.type === 'heartbeat') {
                        setConnectionStatus('connected');
                    } else if (data.type === 'setting_update') {
                        // Refresh current page data
                        window.dispatchEvent(new CustomEvent('refreshData'));
                    } else if (data.type === 'moderation_action') {
                        // Show notification for moderation actions
                        showNotification(`Moderation action: ${data.action}`, 'info');
                    }
                };

                websocket.onclose = () => {
                    console.log('WebSocket disconnected');
                    setConnectionStatus('disconnected');
                    // Attempt to reconnect after 5 seconds
                    setTimeout(connectWebSocket, 5000);
                };

                websocket.onerror = (error) => {
                    console.error('WebSocket error:', error);
                    setConnectionStatus('error');
                };
            };

            const checkAuth = async () => {
                try {
                    const response = await fetch('/api/user/me');
                    if (response.ok) {
                        const userData = await response.json();
                        setUser(userData);
                        if (userData.admin_guilds.length > 0) {
                            setSelectedGuild(userData.admin_guilds[0]);
                        }
                    }
                } catch (error) {
                    console.error('Auth check failed:', error);
                } finally {
                    setLoading(false);
                }
            };

            // Global notification function
            window.showNotification = (message, type = 'info') => {
                // Simple notification - in a real app you'd use a proper notification system
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 p-4 rounded-lg z-50 ${
                    type === 'success' ? 'bg-green-500' :
                    type === 'error' ? 'bg-red-500' :
                    type === 'warning' ? 'bg-yellow-500' : 'bg-blue-500'
                } text-white`;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 3000);
            };
            
            if (loading) {
                return <LoadingScreen />;
            }
            
            if (!user) {
                return <LoginPage />;
            }
            
            return (
                <div className="flex h-screen bg-bg-primary">
                    <Sidebar 
                        user={user}
                        selectedGuild={selectedGuild}
                        currentPage={currentPage}
                        onPageChange={setCurrentPage}
                        onGuildChange={setSelectedGuild}
                    />
                    <MainContent 
                        user={user}
                        selectedGuild={selectedGuild}
                        currentPage={currentPage}
                    />
                </div>
            );
        }
        
        // Loading Screen Component
        function LoadingScreen() {
            return (
                <div className="flex items-center justify-center h-screen bg-bg-primary">
                    <div className="text-center">
                        <div className="w-16 h-16 border-4 border-accent-purple border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                        <h2 className="text-xl font-semibold text-text-primary">Loading Dashboard...</h2>
                        <p className="text-text-secondary mt-2">Please wait while we prepare your experience</p>
                    </div>
                </div>
            );
        }
        
        // Login Page Component
        function LoginPage() {
            return (
                <div className="flex items-center justify-center h-screen bg-bg-primary">
                    <div className="card max-w-md w-full mx-4 text-center">
                        <div className="mb-8">
                            <div className="w-20 h-20 bg-gradient-to-br from-accent-purple to-accent-cyan rounded-full mx-auto mb-4 flex items-center justify-center">
                                <span className="text-2xl font-bold text-white">H</span>
                            </div>
                            <h1 className="text-3xl font-bold text-text-primary mb-2">Hina Bot Dashboard</h1>
                            <p className="text-text-secondary">Manage your Discord server with ease</p>
                        </div>
                        
                        <button 
                            className="button-primary w-full mb-4"
                            onClick={() => window.location.href = '/auth/login'}
                        >
                            Login with Discord
                        </button>
                        
                        <p className="text-text-muted text-sm">
                            You need administrator permissions in at least one server to access the dashboard.
                        </p>
                    </div>
                </div>
            );
        }
        
        // Sidebar Component
        function Sidebar({ user, selectedGuild, currentPage, onPageChange, onGuildChange }) {
            const [collapsed, setCollapsed] = useState(false);

            const menuItems = [
                { id: 'dashboard', name: 'Dashboard', icon: '📊' },
                { id: 'admin', name: 'Admin', icon: '⚙️' },
                { id: 'moderation', name: 'Moderation', icon: '🛡️' },
                { id: 'automod', name: 'AutoMod', icon: '🤖' },
                { id: 'logging', name: 'Logging', icon: '📝' },
                { id: 'music', name: 'Music', icon: '🎵' },
                { id: 'utility', name: 'Utility', icon: '🔧' },
                { id: 'settings', name: 'Settings', icon: '⚙️' }
            ];

            return (
                <div className={`sidebar flex flex-col h-full transition-all duration-300 ${collapsed ? 'w-16' : 'w-64'}`}>
                    {/* Header */}
                    <div className="p-4 border-b border-border-color">
                        <div className="flex items-center justify-between">
                            {!collapsed && (
                                <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 bg-gradient-to-br from-accent-purple to-accent-cyan rounded-lg flex items-center justify-center">
                                        <span className="text-white font-bold">H</span>
                                    </div>
                                    <span className="font-bold text-lg">Hina</span>
                                </div>
                            )}
                            <button
                                onClick={() => setCollapsed(!collapsed)}
                                className="p-2 hover:bg-bg-tertiary rounded-lg transition-colors"
                            >
                                {collapsed ? '→' : '←'}
                            </button>
                        </div>
                    </div>

                    {/* Guild Selector */}
                    {!collapsed && (
                        <div className="p-4 border-b border-border-color">
                            <select
                                className="dropdown w-full p-2"
                                value={selectedGuild?.id || ''}
                                onChange={(e) => {
                                    const guild = user.admin_guilds.find(g => g.id === e.target.value);
                                    onGuildChange(guild);
                                }}
                            >
                                {user.admin_guilds.map(guild => (
                                    <option key={guild.id} value={guild.id}>
                                        {guild.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    )}

                    {/* Navigation Menu */}
                    <nav className="flex-1 p-4 space-y-2">
                        {menuItems.map(item => (
                            <button
                                key={item.id}
                                onClick={() => onPageChange(item.id)}
                                className={`nav-item w-full flex items-center space-x-3 p-3 text-left ${
                                    currentPage === item.id ? 'active' : 'text-text-secondary hover:text-text-primary'
                                }`}
                            >
                                <span className="text-lg">{item.icon}</span>
                                {!collapsed && <span>{item.name}</span>}
                            </button>
                        ))}
                    </nav>

                    {/* User Info */}
                    <div className="p-4 border-t border-border-color">
                        <div className="flex items-center space-x-3">
                            <img
                                src={user.avatar_url}
                                alt={user.display_name}
                                className="w-8 h-8 rounded-full"
                            />
                            {!collapsed && (
                                <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-text-primary truncate">
                                        {user.display_name}
                                    </p>
                                    <button
                                        onClick={() => window.location.href = '/auth/logout'}
                                        className="text-xs text-text-muted hover:text-accent-purple"
                                    >
                                        Logout
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            );
        }

        // Main Content Component
        function MainContent({ user, selectedGuild, currentPage }) {
            const renderPage = () => {
                switch (currentPage) {
                    case 'dashboard':
                        return <DashboardPage selectedGuild={selectedGuild} />;
                    case 'admin':
                        return <AdminPage selectedGuild={selectedGuild} />;
                    case 'moderation':
                        return <ModerationPage selectedGuild={selectedGuild} />;
                    case 'automod':
                        return <AutoModPage selectedGuild={selectedGuild} />;
                    case 'logging':
                        return <LoggingPage selectedGuild={selectedGuild} />;
                    case 'music':
                        return <MusicPage selectedGuild={selectedGuild} />;
                    case 'utility':
                        return <UtilityPage selectedGuild={selectedGuild} />;
                    case 'settings':
                        return <SettingsPage selectedGuild={selectedGuild} />;
                    default:
                        return <DashboardPage selectedGuild={selectedGuild} />;
                }
            };

            return (
                <div className="flex-1 flex flex-col overflow-hidden">
                    {/* Header */}
                    <header className="bg-bg-secondary border-b border-border-color p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-2xl font-bold text-text-primary capitalize">
                                    {currentPage}
                                </h1>
                                {selectedGuild && (
                                    <p className="text-text-secondary">
                                        Managing {selectedGuild.name}
                                    </p>
                                )}
                            </div>
                            <div className="flex items-center space-x-4">
                                <div className="flex items-center space-x-2">
                                    <div className={`w-3 h-3 rounded-full ${
                                        connectionStatus === 'connected' ? 'bg-success' :
                                        connectionStatus === 'error' ? 'bg-error' : 'bg-warning'
                                    }`}></div>
                                    <span className="text-sm text-text-secondary">
                                        {connectionStatus === 'connected' ? 'Connected' :
                                         connectionStatus === 'error' ? 'Connection Error' : 'Connecting...'}
                                    </span>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <div className="w-3 h-3 bg-success rounded-full"></div>
                                    <span className="text-sm text-text-secondary">Bot Online</span>
                                </div>
                            </div>
                        </div>
                    </header>

                    {/* Content */}
                    <main className="flex-1 overflow-auto p-6 scrollbar-thin">
                        {selectedGuild ? renderPage() : (
                            <div className="text-center py-12">
                                <h2 className="text-xl font-semibold text-text-primary mb-2">
                                    No Server Selected
                                </h2>
                                <p className="text-text-secondary">
                                    Please select a server from the sidebar to continue.
                                </p>
                            </div>
                        )}
                    </main>
                </div>
            );
        }

        // Dashboard Page Component
        function DashboardPage({ selectedGuild }) {
            const [stats, setStats] = useState(null);
            const [loading, setLoading] = useState(true);

            useEffect(() => {
                if (selectedGuild) {
                    fetchGuildStats();
                }
            }, [selectedGuild]);

            const fetchGuildStats = async () => {
                try {
                    const response = await fetch(`/api/guilds/${selectedGuild.id}/stats`);
                    if (response.ok) {
                        const data = await response.json();
                        setStats(data);
                    }
                } catch (error) {
                    console.error('Failed to fetch stats:', error);
                } finally {
                    setLoading(false);
                }
            };

            if (loading) {
                return <div className="text-center py-8">Loading dashboard...</div>;
            }

            return (
                <div className="space-y-6 fade-in">
                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <StatCard
                            title="Total Commands"
                            value={stats?.total_commands || 0}
                            icon="⚡"
                            color="from-accent-purple to-accent-cyan"
                        />
                        <StatCard
                            title="Mod Actions"
                            value={stats?.total_mod_actions || 0}
                            icon="🛡️"
                            color="from-green-500 to-emerald-500"
                        />
                        <StatCard
                            title="Active Users"
                            value="1,234"
                            icon="👥"
                            color="from-blue-500 to-cyan-500"
                        />
                        <StatCard
                            title="Uptime"
                            value="99.9%"
                            icon="⏱️"
                            color="from-orange-500 to-red-500"
                        />
                    </div>

                    {/* Recent Activity */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Recent Commands</h3>
                            <div className="space-y-3">
                                {stats?.command_usage?.slice(0, 5).map((cmd, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 bg-bg-tertiary rounded-lg">
                                        <span className="text-text-primary">{cmd.command}</span>
                                        <span className="text-accent-purple font-semibold">{cmd.usage_count}</span>
                                    </div>
                                )) || (
                                    <p className="text-text-secondary">No recent command usage</p>
                                )}
                            </div>
                        </div>

                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Moderation Activity</h3>
                            <div className="space-y-3">
                                {stats?.moderation_actions?.slice(0, 5).map((action, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 bg-bg-tertiary rounded-lg">
                                        <span className="text-text-primary capitalize">{action.action_type}</span>
                                        <span className="text-accent-cyan font-semibold">{action.count}</span>
                                    </div>
                                )) || (
                                    <p className="text-text-secondary">No recent moderation activity</p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="card">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">Quick Actions</h3>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <QuickActionButton
                                title="Set Prefix"
                                icon="⚙️"
                                onClick={() => {/* Handle action */}}
                            />
                            <QuickActionButton
                                title="Mute Role"
                                icon="🔇"
                                onClick={() => {/* Handle action */}}
                            />
                            <QuickActionButton
                                title="Log Channel"
                                icon="📝"
                                onClick={() => {/* Handle action */}}
                            />
                            <QuickActionButton
                                title="Auto Roles"
                                icon="🎭"
                                onClick={() => {/* Handle action */}}
                            />
                        </div>
                    </div>
                </div>
            );
        }

        // Stat Card Component
        function StatCard({ title, value, icon, color }) {
            return (
                <div className="card">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-text-secondary text-sm">{title}</p>
                            <p className="text-2xl font-bold text-text-primary">{value}</p>
                        </div>
                        <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${color} flex items-center justify-center`}>
                            <span className="text-xl">{icon}</span>
                        </div>
                    </div>
                </div>
            );
        }

        // Quick Action Button Component
        function QuickActionButton({ title, icon, onClick }) {
            return (
                <button
                    onClick={onClick}
                    className="p-4 bg-bg-tertiary hover:bg-bg-secondary border border-border-color rounded-lg transition-all duration-200 hover:border-accent-purple group"
                >
                    <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">{icon}</div>
                    <div className="text-sm text-text-secondary group-hover:text-accent-purple">{title}</div>
                </button>
            );
        }

        // Admin Page Component
        function AdminPage({ selectedGuild }) {
            const [settings, setSettings] = useState([]);
            const [prefixes, setPrefixes] = useState([]);
            const [loading, setLoading] = useState(true);

            useEffect(() => {
                if (selectedGuild) {
                    fetchAdminSettings();
                }
            }, [selectedGuild]);

            const fetchAdminSettings = async () => {
                try {
                    const [settingsRes, prefixesRes] = await Promise.all([
                        fetch(`/api/guilds/${selectedGuild.id}/settings`),
                        fetch(`/api/guilds/${selectedGuild.id}/prefixes`)
                    ]);

                    if (settingsRes.ok) {
                        const settingsData = await settingsRes.json();
                        setSettings(settingsData);
                    }

                    if (prefixesRes.ok) {
                        const prefixData = await prefixesRes.json();
                        setPrefixes(prefixData.prefixes);
                    }
                } catch (error) {
                    console.error('Failed to fetch admin settings:', error);
                } finally {
                    setLoading(false);
                }
            };

            if (loading) {
                return <div className="text-center py-8">Loading admin settings...</div>;
            }

            return (
                <div className="space-y-6 fade-in">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Prefix Management */}
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Server Prefixes</h3>
                            <PrefixManager
                                guildId={selectedGuild.id}
                                prefixes={prefixes}
                                onUpdate={setPrefixes}
                            />
                        </div>

                        {/* Basic Settings */}
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Basic Settings</h3>
                            <SettingsForm
                                guildId={selectedGuild.id}
                                settings={settings.filter(s => s.category === 'general')}
                                onUpdate={fetchAdminSettings}
                            />
                        </div>
                    </div>

                    {/* Mass Actions */}
                    <div className="card">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">Mass Actions</h3>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <ActionButton title="Mass Dehoist" description="Remove hoisting characters from nicknames" />
                            <ActionButton title="Mass Rename" description="Reset all user nicknames" />
                            <ActionButton title="Mass Role" description="Add/remove roles from multiple users" />
                            <ActionButton title="Server Lock" description="Lock/unlock all channels" />
                        </div>
                    </div>
                </div>
            );
        }

        // Prefix Manager Component
        function PrefixManager({ guildId, prefixes, onUpdate }) {
            const [newPrefix, setNewPrefix] = useState('');
            const [loading, setLoading] = useState(false);

            const addPrefix = async () => {
                if (!newPrefix.trim()) return;

                setLoading(true);
                try {
                    const response = await fetch(`/api/guilds/${guildId}/prefixes`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ prefixes: [newPrefix.trim()] })
                    });

                    if (response.ok) {
                        onUpdate([...prefixes, newPrefix.trim()]);
                        setNewPrefix('');
                    }
                } catch (error) {
                    console.error('Failed to add prefix:', error);
                } finally {
                    setLoading(false);
                }
            };

            const removePrefix = async (prefix) => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/prefixes/${encodeURIComponent(prefix)}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        onUpdate(prefixes.filter(p => p !== prefix));
                    }
                } catch (error) {
                    console.error('Failed to remove prefix:', error);
                }
            };

            return (
                <div className="space-y-4">
                    <div className="flex space-x-2">
                        <input
                            type="text"
                            value={newPrefix}
                            onChange={(e) => setNewPrefix(e.target.value)}
                            placeholder="Enter new prefix..."
                            className="input-field flex-1"
                            onKeyPress={(e) => e.key === 'Enter' && addPrefix()}
                        />
                        <button
                            onClick={addPrefix}
                            disabled={loading || !newPrefix.trim()}
                            className="button-primary px-4"
                        >
                            Add
                        </button>
                    </div>

                    <div className="space-y-2">
                        {prefixes.map((prefix, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-bg-tertiary rounded-lg">
                                <code className="text-accent-purple">{prefix}</code>
                                <button
                                    onClick={() => removePrefix(prefix)}
                                    className="text-error hover:text-red-400 transition-colors"
                                >
                                    ✕
                                </button>
                            </div>
                        ))}
                        {prefixes.length === 0 && (
                            <p className="text-text-secondary text-center py-4">No custom prefixes set</p>
                        )}
                    </div>
                </div>
            );
        }

        // Settings Form Component
        function SettingsForm({ guildId, settings, onUpdate }) {
            const [values, setValues] = useState({});
            const [loading, setLoading] = useState(false);

            useEffect(() => {
                const initialValues = {};
                settings.forEach(setting => {
                    initialValues[setting.key] = setting.value || setting.default_value;
                });
                setValues(initialValues);
            }, [settings]);

            const updateSetting = async (key, value) => {
                setLoading(true);
                try {
                    const response = await fetch(`/api/guilds/${guildId}/settings/${key}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key, value })
                    });

                    if (response.ok) {
                        setValues(prev => ({ ...prev, [key]: value }));
                        onUpdate();
                    }
                } catch (error) {
                    console.error('Failed to update setting:', error);
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="space-y-4">
                    {settings.map(setting => (
                        <div key={setting.key} className="space-y-2">
                            <label className="block text-sm font-medium text-text-primary">
                                {setting.name}
                            </label>
                            <p className="text-xs text-text-secondary">{setting.description}</p>

                            {setting.type === 'boolean' ? (
                                <div
                                    className={`toggle-switch ${values[setting.key] ? 'active' : ''}`}
                                    onClick={() => updateSetting(setting.key, !values[setting.key])}
                                />
                            ) : setting.type === 'string' ? (
                                <input
                                    type="text"
                                    value={values[setting.key] || ''}
                                    onChange={(e) => setValues(prev => ({ ...prev, [setting.key]: e.target.value }))}
                                    onBlur={(e) => updateSetting(setting.key, e.target.value)}
                                    className="input-field"
                                    placeholder={setting.default_value}
                                />
                            ) : setting.type === 'channel' || setting.type === 'role' ? (
                                <select
                                    value={values[setting.key] || ''}
                                    onChange={(e) => updateSetting(setting.key, e.target.value)}
                                    className="dropdown w-full p-2"
                                >
                                    <option value="">Select {setting.type}...</option>
                                    {/* Options would be populated from API */}
                                </select>
                            ) : null}
                        </div>
                    ))}
                </div>
            );
        }

        // Action Button Component
        function ActionButton({ title, description, onClick }) {
            return (
                <button
                    onClick={onClick}
                    className="p-4 bg-bg-tertiary hover:bg-bg-secondary border border-border-color rounded-lg transition-all duration-200 hover:border-accent-purple text-left group"
                >
                    <h4 className="font-semibold text-text-primary group-hover:text-accent-purple mb-1">
                        {title}
                    </h4>
                    <p className="text-xs text-text-secondary">{description}</p>
                </button>
            );
        }

        // Moderation Page Component
        function ModerationPage({ selectedGuild }) {
            const [activeTab, setActiveTab] = useState('actions');
            const [moderationActions, setModerationActions] = useState([]);
            const [loading, setLoading] = useState(false);

            useEffect(() => {
                if (selectedGuild) {
                    fetchModerationActions();
                }
            }, [selectedGuild]);

            const fetchModerationActions = async () => {
                try {
                    const response = await fetch(`/api/guilds/${selectedGuild.id}/moderation/actions`);
                    if (response.ok) {
                        const data = await response.json();
                        setModerationActions(data);
                    }
                } catch (error) {
                    console.error('Failed to fetch moderation actions:', error);
                }
            };

            const tabs = [
                { id: 'actions', name: 'Quick Actions', icon: '⚡' },
                { id: 'history', name: 'Action History', icon: '📋' },
                { id: 'settings', name: 'Mod Settings', icon: '⚙️' }
            ];

            return (
                <div className="space-y-6 fade-in">
                    {/* Tab Navigation */}
                    <div className="flex space-x-1 bg-bg-tertiary rounded-lg p-1">
                        {tabs.map(tab => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
                                    activeTab === tab.id
                                        ? 'bg-accent-purple text-white'
                                        : 'text-text-secondary hover:text-text-primary'
                                }`}
                            >
                                <span>{tab.icon}</span>
                                <span>{tab.name}</span>
                            </button>
                        ))}
                    </div>

                    {/* Tab Content */}
                    {activeTab === 'actions' && (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <ModerationActionCard
                                title="Kick User"
                                description="Remove a user from the server"
                                icon="👢"
                                color="from-yellow-500 to-orange-500"
                                action={() => openModerationModal('kick')}
                            />
                            <ModerationActionCard
                                title="Ban User"
                                description="Permanently ban a user"
                                icon="🔨"
                                color="from-red-500 to-pink-500"
                                action={() => openModerationModal('ban')}
                            />
                            <ModerationActionCard
                                title="Mute User"
                                description="Temporarily mute a user"
                                icon="🔇"
                                color="from-blue-500 to-cyan-500"
                                action={() => openModerationModal('mute')}
                            />
                            <ModerationActionCard
                                title="Warn User"
                                description="Issue a warning to a user"
                                icon="⚠️"
                                color="from-orange-500 to-red-500"
                                action={() => openModerationModal('warn')}
                            />
                            <ModerationActionCard
                                title="Lock Channel"
                                description="Prevent messages in a channel"
                                icon="🔒"
                                color="from-purple-500 to-indigo-500"
                                action={() => openModerationModal('lock')}
                            />
                            <ModerationActionCard
                                title="Slowmode"
                                description="Set channel slowmode"
                                icon="🐌"
                                color="from-green-500 to-teal-500"
                                action={() => openModerationModal('slowmode')}
                            />
                        </div>
                    )}

                    {activeTab === 'history' && (
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Recent Moderation Actions</h3>
                            <div className="space-y-3">
                                {moderationActions.length > 0 ? moderationActions.map((action, index) => (
                                    <div key={index} className="flex items-center justify-between p-4 bg-bg-tertiary rounded-lg">
                                        <div className="flex items-center space-x-3">
                                            <div className="w-8 h-8 bg-accent-purple rounded-full flex items-center justify-center">
                                                <span className="text-sm">
                                                    {action.type === 'ban' ? '🔨' :
                                                     action.type === 'kick' ? '👢' :
                                                     action.type === 'mute' ? '🔇' : '⚠️'}
                                                </span>
                                            </div>
                                            <div>
                                                <p className="text-text-primary font-medium capitalize">{action.type}</p>
                                                <p className="text-text-secondary text-sm">User ID: {action.user_id}</p>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <p className="text-text-secondary text-sm">
                                                {new Date(action.created_at).toLocaleDateString()}
                                            </p>
                                            {action.reason && (
                                                <p className="text-text-muted text-xs">{action.reason}</p>
                                            )}
                                        </div>
                                    </div>
                                )) : (
                                    <p className="text-text-secondary text-center py-8">No moderation actions recorded</p>
                                )}
                            </div>
                        </div>
                    )}

                    {activeTab === 'settings' && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="card">
                                <h3 className="text-lg font-semibold text-text-primary mb-4">Mute Role</h3>
                                <MuteRoleSelector guildId={selectedGuild.id} />
                            </div>
                            <div className="card">
                                <h3 className="text-lg font-semibold text-text-primary mb-4">Auto Moderation</h3>
                                <AutoModerationSettings guildId={selectedGuild.id} />
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        // Moderation Action Card Component
        function ModerationActionCard({ title, description, icon, color, action }) {
            return (
                <div
                    onClick={action}
                    className="card cursor-pointer hover:scale-105 transition-transform duration-200 group"
                >
                    <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${color} flex items-center justify-center group-hover:scale-110 transition-transform`}>
                            <span className="text-xl">{icon}</span>
                        </div>
                        <div className="flex-1">
                            <h4 className="font-semibold text-text-primary group-hover:text-accent-purple transition-colors">
                                {title}
                            </h4>
                            <p className="text-sm text-text-secondary">{description}</p>
                        </div>
                    </div>
                </div>
            );
        }

        // Mute Role Selector Component
        function MuteRoleSelector({ guildId }) {
            const [roles, setRoles] = useState([]);
            const [selectedRole, setSelectedRole] = useState('');
            const [loading, setLoading] = useState(false);

            useEffect(() => {
                fetchRoles();
            }, [guildId]);

            const fetchRoles = async () => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/roles`);
                    if (response.ok) {
                        const data = await response.json();
                        setRoles(data);
                    }
                } catch (error) {
                    console.error('Failed to fetch roles:', error);
                }
            };

            const updateMuteRole = async (roleId) => {
                setLoading(true);
                try {
                    const response = await fetch(`/api/guilds/${guildId}/settings/mute_role`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key: 'mute_role', value: roleId })
                    });

                    if (response.ok) {
                        setSelectedRole(roleId);
                    }
                } catch (error) {
                    console.error('Failed to update mute role:', error);
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="space-y-4">
                    <select
                        value={selectedRole}
                        onChange={(e) => updateMuteRole(e.target.value)}
                        disabled={loading}
                        className="dropdown w-full p-3"
                    >
                        <option value="">Select mute role...</option>
                        {roles.map(role => (
                            <option key={role.id} value={role.id}>
                                {role.name}
                            </option>
                        ))}
                    </select>
                    <p className="text-xs text-text-secondary">
                        The mute role will be assigned to users when they are muted.
                    </p>
                </div>
            );
        }

        // Auto Moderation Settings Component
        function AutoModerationSettings({ guildId }) {
            const [settings, setSettings] = useState({
                antiInvite: false,
                antiSpam: false,
                autoDelete: false
            });

            const updateSetting = async (key, value) => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/settings/${key}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key, value })
                    });

                    if (response.ok) {
                        setSettings(prev => ({ ...prev, [key]: value }));
                    }
                } catch (error) {
                    console.error('Failed to update setting:', error);
                }
            };

            return (
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-text-primary font-medium">Anti-Invite</p>
                            <p className="text-xs text-text-secondary">Automatically delete Discord invite links</p>
                        </div>
                        <div
                            className={`toggle-switch ${settings.antiInvite ? 'active' : ''}`}
                            onClick={() => updateSetting('antiInvite', !settings.antiInvite)}
                        />
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-text-primary font-medium">Anti-Spam</p>
                            <p className="text-xs text-text-secondary">Detect and prevent spam messages</p>
                        </div>
                        <div
                            className={`toggle-switch ${settings.antiSpam ? 'active' : ''}`}
                            onClick={() => updateSetting('antiSpam', !settings.antiSpam)}
                        />
                    </div>

                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-text-primary font-medium">Auto Delete</p>
                            <p className="text-xs text-text-secondary">Auto-delete messages containing banned words</p>
                        </div>
                        <div
                            className={`toggle-switch ${settings.autoDelete ? 'active' : ''}`}
                            onClick={() => updateSetting('autoDelete', !settings.autoDelete)}
                        />
                    </div>
                </div>
            );
        }

        // Global function to open moderation modal
        function openModerationModal(action) {
            // This would open a modal for the specific moderation action
            alert(`Opening ${action} modal - Feature coming soon!`);
        }

        function AutoModPage({ selectedGuild }) {
            const [activeTab, setActiveTab] = useState('warnings');
            const [warnings, setWarnings] = useState([]);
            const [autoRoles, setAutoRoles] = useState([]);
            const [filters, setFilters] = useState([]);

            const tabs = [
                { id: 'warnings', name: 'Warning System', icon: '⚠️' },
                { id: 'autoroles', name: 'Auto Roles', icon: '🎭' },
                { id: 'filters', name: 'Word Filters', icon: '🚫' },
                { id: 'protection', name: 'Server Protection', icon: '🛡️' }
            ];

            return (
                <div className="space-y-6 fade-in">
                    {/* Tab Navigation */}
                    <div className="flex space-x-1 bg-bg-tertiary rounded-lg p-1">
                        {tabs.map(tab => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-all ${
                                    activeTab === tab.id
                                        ? 'bg-accent-purple text-white'
                                        : 'text-text-secondary hover:text-text-primary'
                                }`}
                            >
                                <span>{tab.icon}</span>
                                <span>{tab.name}</span>
                            </button>
                        ))}
                    </div>

                    {/* Warning System Tab */}
                    {activeTab === 'warnings' && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="card">
                                <h3 className="text-lg font-semibold text-text-primary mb-4">Warning Configuration</h3>
                                <WarningSystemConfig guildId={selectedGuild.id} />
                            </div>
                            <div className="card">
                                <h3 className="text-lg font-semibold text-text-primary mb-4">Recent Warnings</h3>
                                <RecentWarnings guildId={selectedGuild.id} />
                            </div>
                        </div>
                    )}

                    {/* Auto Roles Tab */}
                    {activeTab === 'autoroles' && (
                        <div className="space-y-6">
                            <div className="card">
                                <h3 className="text-lg font-semibold text-text-primary mb-4">Auto Role Assignment</h3>
                                <AutoRoleManager guildId={selectedGuild.id} />
                            </div>
                            <div className="card">
                                <h3 className="text-lg font-semibold text-text-primary mb-4">Sticky Roles</h3>
                                <StickyRoleManager guildId={selectedGuild.id} />
                            </div>
                        </div>
                    )}

                    {/* Word Filters Tab */}
                    {activeTab === 'filters' && (
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Word Filter Management</h3>
                            <WordFilterManager guildId={selectedGuild.id} />
                        </div>
                    )}

                    {/* Server Protection Tab */}
                    {activeTab === 'protection' && (
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="card">
                                <h3 className="text-lg font-semibold text-text-primary mb-4">Anti-Raid Protection</h3>
                                <AntiRaidSettings guildId={selectedGuild.id} />
                            </div>
                            <div className="card">
                                <h3 className="text-lg font-semibold text-text-primary mb-4">Spam Protection</h3>
                                <SpamProtectionSettings guildId={selectedGuild.id} />
                            </div>
                        </div>
                    )}
                </div>
            );
        }

        // Warning System Configuration Component
        function WarningSystemConfig({ guildId }) {
            const [config, setConfig] = useState({
                maxWarnings: 3,
                autoAction: 'mute',
                warningDuration: 30
            });

            const updateConfig = async (key, value) => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/settings/warning_${key}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key: `warning_${key}`, value })
                    });

                    if (response.ok) {
                        setConfig(prev => ({ ...prev, [key]: value }));
                    }
                } catch (error) {
                    console.error('Failed to update warning config:', error);
                }
            };

            return (
                <div className="space-y-4">
                    <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                            Maximum Warnings Before Action
                        </label>
                        <input
                            type="number"
                            value={config.maxWarnings}
                            onChange={(e) => updateConfig('maxWarnings', parseInt(e.target.value))}
                            className="input-field"
                            min="1"
                            max="10"
                        />
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                            Auto Action on Max Warnings
                        </label>
                        <select
                            value={config.autoAction}
                            onChange={(e) => updateConfig('autoAction', e.target.value)}
                            className="dropdown w-full p-2"
                        >
                            <option value="none">No Action</option>
                            <option value="mute">Mute User</option>
                            <option value="kick">Kick User</option>
                            <option value="ban">Ban User</option>
                        </select>
                    </div>

                    <div>
                        <label className="block text-sm font-medium text-text-primary mb-2">
                            Warning Duration (days)
                        </label>
                        <input
                            type="number"
                            value={config.warningDuration}
                            onChange={(e) => updateConfig('warningDuration', parseInt(e.target.value))}
                            className="input-field"
                            min="1"
                            max="365"
                        />
                        <p className="text-xs text-text-secondary mt-1">
                            Warnings will automatically expire after this duration
                        </p>
                    </div>
                </div>
            );
        }

        // Recent Warnings Component
        function RecentWarnings({ guildId }) {
            const [warnings, setWarnings] = useState([]);

            useEffect(() => {
                // Fetch recent warnings
                // This would be implemented with actual API call
                setWarnings([
                    { id: 1, user: 'User#1234', reason: 'Spam', moderator: 'Mod#5678', date: '2025-01-19' },
                    { id: 2, user: 'User#9876', reason: 'Inappropriate language', moderator: 'Mod#5678', date: '2025-01-18' }
                ]);
            }, [guildId]);

            return (
                <div className="space-y-3">
                    {warnings.length > 0 ? warnings.map(warning => (
                        <div key={warning.id} className="p-3 bg-bg-tertiary rounded-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-text-primary font-medium">{warning.user}</p>
                                    <p className="text-text-secondary text-sm">{warning.reason}</p>
                                </div>
                                <div className="text-right">
                                    <p className="text-text-secondary text-xs">{warning.date}</p>
                                    <p className="text-text-muted text-xs">by {warning.moderator}</p>
                                </div>
                            </div>
                        </div>
                    )) : (
                        <p className="text-text-secondary text-center py-4">No recent warnings</p>
                    )}
                </div>
            );
        }

        // Auto Role Manager Component
        function AutoRoleManager({ guildId }) {
            const [autoRoles, setAutoRoles] = useState([]);
            const [roles, setRoles] = useState([]);
            const [selectedRole, setSelectedRole] = useState('');

            useEffect(() => {
                fetchRoles();
                fetchAutoRoles();
            }, [guildId]);

            const fetchRoles = async () => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/roles`);
                    if (response.ok) {
                        const data = await response.json();
                        setRoles(data);
                    }
                } catch (error) {
                    console.error('Failed to fetch roles:', error);
                }
            };

            const fetchAutoRoles = async () => {
                // This would fetch current auto roles from API
                setAutoRoles([]);
            };

            const addAutoRole = async () => {
                if (!selectedRole) return;

                try {
                    const response = await fetch(`/api/guilds/${guildId}/autoroles`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ roleId: selectedRole })
                    });

                    if (response.ok) {
                        const role = roles.find(r => r.id === selectedRole);
                        setAutoRoles(prev => [...prev, role]);
                        setSelectedRole('');
                    }
                } catch (error) {
                    console.error('Failed to add auto role:', error);
                }
            };

            const removeAutoRole = async (roleId) => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/autoroles/${roleId}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        setAutoRoles(prev => prev.filter(role => role.id !== roleId));
                    }
                } catch (error) {
                    console.error('Failed to remove auto role:', error);
                }
            };

            return (
                <div className="space-y-4">
                    <div className="flex space-x-2">
                        <select
                            value={selectedRole}
                            onChange={(e) => setSelectedRole(e.target.value)}
                            className="dropdown flex-1 p-2"
                        >
                            <option value="">Select role to auto-assign...</option>
                            {roles.filter(role => !autoRoles.find(ar => ar.id === role.id)).map(role => (
                                <option key={role.id} value={role.id}>
                                    {role.name}
                                </option>
                            ))}
                        </select>
                        <button
                            onClick={addAutoRole}
                            disabled={!selectedRole}
                            className="button-primary px-4"
                        >
                            Add
                        </button>
                    </div>

                    <div className="space-y-2">
                        {autoRoles.map(role => (
                            <div key={role.id} className="flex items-center justify-between p-3 bg-bg-tertiary rounded-lg">
                                <div className="flex items-center space-x-3">
                                    <div
                                        className="w-4 h-4 rounded-full"
                                        style={{ backgroundColor: `#${role.color.toString(16).padStart(6, '0')}` }}
                                    />
                                    <span className="text-text-primary">{role.name}</span>
                                </div>
                                <button
                                    onClick={() => removeAutoRole(role.id)}
                                    className="text-error hover:text-red-400 transition-colors"
                                >
                                    ✕
                                </button>
                            </div>
                        ))}
                        {autoRoles.length === 0 && (
                            <p className="text-text-secondary text-center py-4">No auto roles configured</p>
                        )}
                    </div>

                    <p className="text-xs text-text-secondary">
                        These roles will be automatically assigned to new members when they join the server.
                    </p>
                </div>
            );
        }

        function LoggingPage({ selectedGuild }) {
            const [logChannel, setLogChannel] = useState('');
            const [channels, setChannels] = useState([]);
            const [logEvents, setLogEvents] = useState({
                joins: false,
                leaves: false,
                messages: false,
                voice: false,
                roles: false,
                channels: false,
                bans: false,
                kicks: false
            });

            useEffect(() => {
                if (selectedGuild) {
                    fetchChannels();
                    fetchLogSettings();
                }
            }, [selectedGuild]);

            const fetchChannels = async () => {
                try {
                    const response = await fetch(`/api/guilds/${selectedGuild.id}/channels`);
                    if (response.ok) {
                        const data = await response.json();
                        setChannels(data.filter(ch => ch.type === 'text'));
                    }
                } catch (error) {
                    console.error('Failed to fetch channels:', error);
                }
            };

            const fetchLogSettings = async () => {
                try {
                    const response = await fetch(`/api/guilds/${selectedGuild.id}/settings`);
                    if (response.ok) {
                        const settings = await response.json();
                        const logChannelSetting = settings.find(s => s.key === 'log_channel');
                        if (logChannelSetting) {
                            setLogChannel(logChannelSetting.value || '');
                        }

                        // Set log events from settings
                        const events = {};
                        settings.forEach(setting => {
                            if (setting.key.startsWith('log_')) {
                                const eventType = setting.key.replace('log_', '');
                                if (eventType !== 'channel') {
                                    events[eventType] = setting.value || false;
                                }
                            }
                        });
                        setLogEvents(prev => ({ ...prev, ...events }));
                    }
                } catch (error) {
                    console.error('Failed to fetch log settings:', error);
                }
            };

            const updateLogChannel = async (channelId) => {
                try {
                    const response = await fetch(`/api/guilds/${selectedGuild.id}/settings/log_channel`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key: 'log_channel', value: channelId })
                    });

                    if (response.ok) {
                        setLogChannel(channelId);
                    }
                } catch (error) {
                    console.error('Failed to update log channel:', error);
                }
            };

            const updateLogEvent = async (eventType, enabled) => {
                try {
                    const response = await fetch(`/api/guilds/${selectedGuild.id}/settings/log_${eventType}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key: `log_${eventType}`, value: enabled })
                    });

                    if (response.ok) {
                        setLogEvents(prev => ({ ...prev, [eventType]: enabled }));
                    }
                } catch (error) {
                    console.error('Failed to update log event:', error);
                }
            };

            const logEventTypes = [
                { key: 'joins', name: 'Member Joins', description: 'Log when members join the server', icon: '👋' },
                { key: 'leaves', name: 'Member Leaves', description: 'Log when members leave the server', icon: '👋' },
                { key: 'messages', name: 'Message Events', description: 'Log message edits and deletions', icon: '💬' },
                { key: 'voice', name: 'Voice Events', description: 'Log voice channel activity', icon: '🔊' },
                { key: 'roles', name: 'Role Changes', description: 'Log role assignments and removals', icon: '🎭' },
                { key: 'channels', name: 'Channel Events', description: 'Log channel creation, deletion, and edits', icon: '📝' },
                { key: 'bans', name: 'Bans & Unbans', description: 'Log ban and unban actions', icon: '🔨' },
                { key: 'kicks', name: 'Kicks', description: 'Log kick actions', icon: '👢' }
            ];

            return (
                <div className="space-y-6 fade-in">
                    {/* Log Channel Selection */}
                    <div className="card">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">Log Channel</h3>
                        <div className="space-y-4">
                            <select
                                value={logChannel}
                                onChange={(e) => updateLogChannel(e.target.value)}
                                className="dropdown w-full p-3"
                            >
                                <option value="">Select log channel...</option>
                                {channels.map(channel => (
                                    <option key={channel.id} value={channel.id}>
                                        #{channel.name}
                                    </option>
                                ))}
                            </select>
                            <p className="text-xs text-text-secondary">
                                Choose the channel where log messages will be sent. Make sure the bot has permission to send messages in this channel.
                            </p>
                        </div>
                    </div>

                    {/* Log Events Configuration */}
                    <div className="card">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">Log Events</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            {logEventTypes.map(eventType => (
                                <div key={eventType.key} className="flex items-center justify-between p-4 bg-bg-tertiary rounded-lg">
                                    <div className="flex items-center space-x-3">
                                        <span className="text-xl">{eventType.icon}</span>
                                        <div>
                                            <p className="text-text-primary font-medium">{eventType.name}</p>
                                            <p className="text-xs text-text-secondary">{eventType.description}</p>
                                        </div>
                                    </div>
                                    <div
                                        className={`toggle-switch ${logEvents[eventType.key] ? 'active' : ''}`}
                                        onClick={() => updateLogEvent(eventType.key, !logEvents[eventType.key])}
                                    />
                                </div>
                            ))}
                        </div>
                    </div>

                    {/* Log Preview */}
                    <div className="card">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">Recent Log Entries</h3>
                        <div className="space-y-3">
                            <LogEntry
                                type="join"
                                message="User#1234 joined the server"
                                timestamp="2 minutes ago"
                                icon="👋"
                                color="text-green-400"
                            />
                            <LogEntry
                                type="message"
                                message="Message deleted in #general by User#5678"
                                timestamp="5 minutes ago"
                                icon="🗑️"
                                color="text-red-400"
                            />
                            <LogEntry
                                type="role"
                                message="Member role added to User#9876"
                                timestamp="10 minutes ago"
                                icon="🎭"
                                color="text-blue-400"
                            />
                        </div>
                    </div>
                </div>
            );
        }

        // Log Entry Component
        function LogEntry({ type, message, timestamp, icon, color }) {
            return (
                <div className="flex items-center space-x-3 p-3 bg-bg-tertiary rounded-lg">
                    <span className={`text-lg ${color}`}>{icon}</span>
                    <div className="flex-1">
                        <p className="text-text-primary">{message}</p>
                        <p className="text-xs text-text-secondary">{timestamp}</p>
                    </div>
                </div>
            );
        }

        function MusicPage({ selectedGuild }) {
            const [musicSettings, setMusicSettings] = useState({
                djRole: '',
                djLock: false,
                maxQueueSize: 100,
                defaultVolume: 50,
                autoLeave: true
            });
            const [roles, setRoles] = useState([]);

            useEffect(() => {
                if (selectedGuild) {
                    fetchRoles();
                    fetchMusicSettings();
                }
            }, [selectedGuild]);

            const fetchRoles = async () => {
                try {
                    const response = await fetch(`/api/guilds/${selectedGuild.id}/roles`);
                    if (response.ok) {
                        const data = await response.json();
                        setRoles(data);
                    }
                } catch (error) {
                    console.error('Failed to fetch roles:', error);
                }
            };

            const fetchMusicSettings = async () => {
                // Fetch music settings from API
                // This would be implemented with actual API call
            };

            const updateMusicSetting = async (key, value) => {
                try {
                    const response = await fetch(`/api/guilds/${selectedGuild.id}/settings/music_${key}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key: `music_${key}`, value })
                    });

                    if (response.ok) {
                        setMusicSettings(prev => ({ ...prev, [key]: value }));
                        showNotification('Music setting updated successfully', 'success');
                    }
                } catch (error) {
                    console.error('Failed to update music setting:', error);
                    showNotification('Failed to update music setting', 'error');
                }
            };

            return (
                <div className="space-y-6 fade-in">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* DJ Settings */}
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">DJ Settings</h3>
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-text-primary mb-2">
                                        DJ Role
                                    </label>
                                    <select
                                        value={musicSettings.djRole}
                                        onChange={(e) => updateMusicSetting('djRole', e.target.value)}
                                        className="dropdown w-full p-2"
                                    >
                                        <option value="">No DJ role required</option>
                                        {roles.map(role => (
                                            <option key={role.id} value={role.id}>
                                                {role.name}
                                            </option>
                                        ))}
                                    </select>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-text-primary font-medium">DJ Lock</p>
                                        <p className="text-xs text-text-secondary">Only DJ role can control music</p>
                                    </div>
                                    <div
                                        className={`toggle-switch ${musicSettings.djLock ? 'active' : ''}`}
                                        onClick={() => updateMusicSetting('djLock', !musicSettings.djLock)}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Playback Settings */}
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Playback Settings</h3>
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-text-primary mb-2">
                                        Max Queue Size
                                    </label>
                                    <input
                                        type="number"
                                        value={musicSettings.maxQueueSize}
                                        onChange={(e) => updateMusicSetting('maxQueueSize', parseInt(e.target.value))}
                                        className="input-field"
                                        min="10"
                                        max="1000"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-text-primary mb-2">
                                        Default Volume (%)
                                    </label>
                                    <input
                                        type="range"
                                        value={musicSettings.defaultVolume}
                                        onChange={(e) => updateMusicSetting('defaultVolume', parseInt(e.target.value))}
                                        className="w-full"
                                        min="1"
                                        max="100"
                                    />
                                    <div className="text-center text-text-secondary text-sm mt-1">
                                        {musicSettings.defaultVolume}%
                                    </div>
                                </div>

                                <div className="flex items-center justify-between">
                                    <div>
                                        <p className="text-text-primary font-medium">Auto Leave</p>
                                        <p className="text-xs text-text-secondary">Leave voice channel when inactive</p>
                                    </div>
                                    <div
                                        className={`toggle-switch ${musicSettings.autoLeave ? 'active' : ''}`}
                                        onClick={() => updateMusicSetting('autoLeave', !musicSettings.autoLeave)}
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Music Commands */}
                    <div className="card">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">Music Commands</h3>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <CommandButton name="play" description="Play music from URL or search" />
                            <CommandButton name="pause" description="Pause current track" />
                            <CommandButton name="skip" description="Skip to next track" />
                            <CommandButton name="queue" description="Show current queue" />
                            <CommandButton name="volume" description="Adjust playback volume" />
                            <CommandButton name="loop" description="Loop current track or queue" />
                            <CommandButton name="shuffle" description="Shuffle the queue" />
                            <CommandButton name="clear" description="Clear the queue" />
                        </div>
                    </div>
                </div>
            );
        }

        function UtilityPage({ selectedGuild }) {
            const utilityCommands = [
                { name: 'avatar', description: 'Show user avatar', category: 'User Info' },
                { name: 'serverinfo', description: 'Show server information', category: 'Server Info' },
                { name: 'userinfo', description: 'Show user information', category: 'User Info' },
                { name: 'color', description: 'Show color information', category: 'Tools' },
                { name: 'embed', description: 'Create custom embeds', category: 'Tools' },
                { name: 'poll', description: 'Create polls', category: 'Tools' },
                { name: 'remind', description: 'Set reminders', category: 'Tools' },
                { name: 'translate', description: 'Translate text', category: 'Tools' }
            ];

            const categories = [...new Set(utilityCommands.map(cmd => cmd.category))];

            return (
                <div className="space-y-6 fade-in">
                    {categories.map(category => (
                        <div key={category} className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">{category}</h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {utilityCommands
                                    .filter(cmd => cmd.category === category)
                                    .map(command => (
                                        <CommandButton
                                            key={command.name}
                                            name={command.name}
                                            description={command.description}
                                        />
                                    ))
                                }
                            </div>
                        </div>
                    ))}
                </div>
            );
        }

        function SettingsPage({ selectedGuild }) {
            const [botSettings, setBotSettings] = useState({
                language: 'en',
                timezone: 'UTC',
                dateFormat: 'MM/DD/YYYY',
                embedColor: '#8b5cf6'
            });

            const updateBotSetting = async (key, value) => {
                try {
                    const response = await fetch(`/api/guilds/${selectedGuild.id}/settings/bot_${key}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key: `bot_${key}`, value })
                    });

                    if (response.ok) {
                        setBotSettings(prev => ({ ...prev, [key]: value }));
                        showNotification('Bot setting updated successfully', 'success');
                    }
                } catch (error) {
                    console.error('Failed to update bot setting:', error);
                    showNotification('Failed to update bot setting', 'error');
                }
            };

            return (
                <div className="space-y-6 fade-in">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* General Settings */}
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">General Settings</h3>
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-text-primary mb-2">
                                        Language
                                    </label>
                                    <select
                                        value={botSettings.language}
                                        onChange={(e) => updateBotSetting('language', e.target.value)}
                                        className="dropdown w-full p-2"
                                    >
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="ja">Japanese</option>
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-text-primary mb-2">
                                        Timezone
                                    </label>
                                    <select
                                        value={botSettings.timezone}
                                        onChange={(e) => updateBotSetting('timezone', e.target.value)}
                                        className="dropdown w-full p-2"
                                    >
                                        <option value="UTC">UTC</option>
                                        <option value="America/New_York">Eastern Time</option>
                                        <option value="America/Chicago">Central Time</option>
                                        <option value="America/Denver">Mountain Time</option>
                                        <option value="America/Los_Angeles">Pacific Time</option>
                                        <option value="Europe/London">London</option>
                                        <option value="Europe/Paris">Paris</option>
                                        <option value="Asia/Tokyo">Tokyo</option>
                                    </select>
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-text-primary mb-2">
                                        Date Format
                                    </label>
                                    <select
                                        value={botSettings.dateFormat}
                                        onChange={(e) => updateBotSetting('dateFormat', e.target.value)}
                                        className="dropdown w-full p-2"
                                    >
                                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        {/* Appearance Settings */}
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Appearance</h3>
                            <div className="space-y-4">
                                <div>
                                    <label className="block text-sm font-medium text-text-primary mb-2">
                                        Embed Color
                                    </label>
                                    <input
                                        type="color"
                                        value={botSettings.embedColor}
                                        onChange={(e) => updateBotSetting('embedColor', e.target.value)}
                                        className="w-full h-12 rounded-lg border border-border-color"
                                    />
                                    <p className="text-xs text-text-secondary mt-1">
                                        Default color for bot embeds
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Bot Information */}
                    <div className="card">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">Bot Information</h3>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div className="text-center">
                                <p className="text-2xl font-bold text-accent-purple">150</p>
                                <p className="text-text-secondary">Servers</p>
                            </div>
                            <div className="text-center">
                                <p className="text-2xl font-bold text-accent-cyan">50,000</p>
                                <p className="text-text-secondary">Users</p>
                            </div>
                            <div className="text-center">
                                <p className="text-2xl font-bold text-success">99.9%</p>
                                <p className="text-text-secondary">Uptime</p>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // Command Button Component
        function CommandButton({ name, description }) {
            return (
                <div className="p-4 bg-bg-tertiary hover:bg-bg-secondary border border-border-color rounded-lg transition-all duration-200 hover:border-accent-purple group cursor-pointer">
                    <h4 className="font-semibold text-text-primary group-hover:text-accent-purple mb-1">
                        {name}
                    </h4>
                    <p className="text-xs text-text-secondary">{description}</p>
                </div>
            );
        }

        // Missing AutoMod Components
        function StickyRoleManager({ guildId }) {
            const [stickyRoles, setStickyRoles] = useState(false);

            const updateStickyRoles = async (enabled) => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/settings/sticky_roles`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key: 'sticky_roles', value: enabled })
                    });

                    if (response.ok) {
                        setStickyRoles(enabled);
                        showNotification('Sticky roles setting updated', 'success');
                    }
                } catch (error) {
                    console.error('Failed to update sticky roles:', error);
                }
            };

            return (
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-text-primary font-medium">Enable Sticky Roles</p>
                            <p className="text-xs text-text-secondary">
                                Automatically reassign roles when users rejoin the server
                            </p>
                        </div>
                        <div
                            className={`toggle-switch ${stickyRoles ? 'active' : ''}`}
                            onClick={() => updateStickyRoles(!stickyRoles)}
                        />
                    </div>

                    <div className="p-3 bg-bg-tertiary rounded-lg">
                        <p className="text-text-secondary text-sm">
                            When enabled, users who leave and rejoin the server will automatically
                            receive the same roles they had before leaving (excluding @everyone).
                        </p>
                    </div>
                </div>
            );
        }

        function WordFilterManager({ guildId }) {
            const [filters, setFilters] = useState([]);
            const [newFilter, setNewFilter] = useState('');
            const [filterAction, setFilterAction] = useState('delete');

            const addFilter = async () => {
                if (!newFilter.trim()) return;

                try {
                    const response = await fetch(`/api/guilds/${guildId}/filters`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            word: newFilter.trim(),
                            action: filterAction
                        })
                    });

                    if (response.ok) {
                        setFilters(prev => [...prev, { word: newFilter.trim(), action: filterAction }]);
                        setNewFilter('');
                        showNotification('Filter added successfully', 'success');
                    }
                } catch (error) {
                    console.error('Failed to add filter:', error);
                }
            };

            const removeFilter = async (word) => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/filters/${encodeURIComponent(word)}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        setFilters(prev => prev.filter(f => f.word !== word));
                        showNotification('Filter removed successfully', 'success');
                    }
                } catch (error) {
                    console.error('Failed to remove filter:', error);
                }
            };

            return (
                <div className="space-y-4">
                    <div className="flex space-x-2">
                        <input
                            type="text"
                            value={newFilter}
                            onChange={(e) => setNewFilter(e.target.value)}
                            placeholder="Enter word or phrase to filter..."
                            className="input-field flex-1"
                            onKeyPress={(e) => e.key === 'Enter' && addFilter()}
                        />
                        <select
                            value={filterAction}
                            onChange={(e) => setFilterAction(e.target.value)}
                            className="dropdown"
                        >
                            <option value="delete">Delete</option>
                            <option value="warn">Warn</option>
                            <option value="mute">Mute</option>
                        </select>
                        <button
                            onClick={addFilter}
                            disabled={!newFilter.trim()}
                            className="button-primary px-4"
                        >
                            Add
                        </button>
                    </div>

                    <div className="space-y-2">
                        {filters.map((filter, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-bg-tertiary rounded-lg">
                                <div>
                                    <span className="text-text-primary font-medium">{filter.word}</span>
                                    <span className="ml-2 text-xs text-text-secondary">
                                        Action: {filter.action}
                                    </span>
                                </div>
                                <button
                                    onClick={() => removeFilter(filter.word)}
                                    className="text-error hover:text-red-400 transition-colors"
                                >
                                    ✕
                                </button>
                            </div>
                        ))}
                        {filters.length === 0 && (
                            <p className="text-text-secondary text-center py-4">No word filters configured</p>
                        )}
                    </div>
                </div>
            );
        }

        function AntiRaidSettings({ guildId }) {
            const [settings, setSettings] = useState({
                enabled: false,
                joinThreshold: 10,
                timeWindow: 60,
                action: 'lockdown'
            });

            const updateSetting = async (key, value) => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/settings/antiraid_${key}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key: `antiraid_${key}`, value })
                    });

                    if (response.ok) {
                        setSettings(prev => ({ ...prev, [key]: value }));
                        showNotification('Anti-raid setting updated', 'success');
                    }
                } catch (error) {
                    console.error('Failed to update anti-raid setting:', error);
                }
            };

            return (
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-text-primary font-medium">Enable Anti-Raid</p>
                            <p className="text-xs text-text-secondary">Automatically detect and prevent raids</p>
                        </div>
                        <div
                            className={`toggle-switch ${settings.enabled ? 'active' : ''}`}
                            onClick={() => updateSetting('enabled', !settings.enabled)}
                        />
                    </div>

                    {settings.enabled && (
                        <>
                            <div>
                                <label className="block text-sm font-medium text-text-primary mb-2">
                                    Join Threshold
                                </label>
                                <input
                                    type="number"
                                    value={settings.joinThreshold}
                                    onChange={(e) => updateSetting('joinThreshold', parseInt(e.target.value))}
                                    className="input-field"
                                    min="5"
                                    max="50"
                                />
                                <p className="text-xs text-text-secondary mt-1">
                                    Number of joins to trigger anti-raid
                                </p>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-text-primary mb-2">
                                    Time Window (seconds)
                                </label>
                                <input
                                    type="number"
                                    value={settings.timeWindow}
                                    onChange={(e) => updateSetting('timeWindow', parseInt(e.target.value))}
                                    className="input-field"
                                    min="30"
                                    max="300"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-text-primary mb-2">
                                    Action
                                </label>
                                <select
                                    value={settings.action}
                                    onChange={(e) => updateSetting('action', e.target.value)}
                                    className="dropdown w-full p-2"
                                >
                                    <option value="lockdown">Server Lockdown</option>
                                    <option value="kick">Kick New Members</option>
                                    <option value="ban">Ban New Members</option>
                                </select>
                            </div>
                        </>
                    )}
                </div>
            );
        }

        function SpamProtectionSettings({ guildId }) {
            const [settings, setSettings] = useState({
                enabled: false,
                messageLimit: 5,
                timeWindow: 10,
                action: 'mute',
                ignoreMods: true
            });

            const updateSetting = async (key, value) => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/settings/spam_${key}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key: `spam_${key}`, value })
                    });

                    if (response.ok) {
                        setSettings(prev => ({ ...prev, [key]: value }));
                        showNotification('Spam protection setting updated', 'success');
                    }
                } catch (error) {
                    console.error('Failed to update spam protection setting:', error);
                }
            };

            return (
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-text-primary font-medium">Enable Spam Protection</p>
                            <p className="text-xs text-text-secondary">Automatically detect and prevent spam</p>
                        </div>
                        <div
                            className={`toggle-switch ${settings.enabled ? 'active' : ''}`}
                            onClick={() => updateSetting('enabled', !settings.enabled)}
                        />
                    </div>

                    {settings.enabled && (
                        <>
                            <div>
                                <label className="block text-sm font-medium text-text-primary mb-2">
                                    Message Limit
                                </label>
                                <input
                                    type="number"
                                    value={settings.messageLimit}
                                    onChange={(e) => updateSetting('messageLimit', parseInt(e.target.value))}
                                    className="input-field"
                                    min="3"
                                    max="20"
                                />
                                <p className="text-xs text-text-secondary mt-1">
                                    Maximum messages in time window
                                </p>
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-text-primary mb-2">
                                    Time Window (seconds)
                                </label>
                                <input
                                    type="number"
                                    value={settings.timeWindow}
                                    onChange={(e) => updateSetting('timeWindow', parseInt(e.target.value))}
                                    className="input-field"
                                    min="5"
                                    max="60"
                                />
                            </div>

                            <div>
                                <label className="block text-sm font-medium text-text-primary mb-2">
                                    Action
                                </label>
                                <select
                                    value={settings.action}
                                    onChange={(e) => updateSetting('action', e.target.value)}
                                    className="dropdown w-full p-2"
                                >
                                    <option value="warn">Warn User</option>
                                    <option value="mute">Mute User</option>
                                    <option value="kick">Kick User</option>
                                    <option value="ban">Ban User</option>
                                </select>
                            </div>

                            <div className="flex items-center justify-between">
                                <div>
                                    <p className="text-text-primary font-medium">Ignore Moderators</p>
                                    <p className="text-xs text-text-secondary">Don't apply spam protection to mods</p>
                                </div>
                                <div
                                    className={`toggle-switch ${settings.ignoreMods ? 'active' : ''}`}
                                    onClick={() => updateSetting('ignoreMods', !settings.ignoreMods)}
                                />
                            </div>
                        </>
                    )}
                </div>
            );
        }

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
