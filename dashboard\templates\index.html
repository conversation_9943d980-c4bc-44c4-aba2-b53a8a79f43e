<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>na <PERSON> Dashboard</title>
    <link rel="icon" type="image/x-icon" href="/static/favicon.ico">
    
    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <!-- Babel for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom CSS for the dark theme -->
    <style>
        :root {
            --bg-primary: #1a1a2e;
            --bg-secondary: #16213e;
            --bg-tertiary: #0f172a;
            --accent-purple: #8b5cf6;
            --accent-cyan: #06b6d4;
            --text-primary: #f8fafc;
            --text-secondary: #cbd5e1;
            --text-muted: #64748b;
            --border-color: #334155;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
        }
        
        body {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .sidebar {
            background: linear-gradient(180deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
            border-right: 1px solid var(--border-color);
        }
        
        .nav-item {
            transition: all 0.2s ease;
            border-radius: 8px;
            margin: 4px 0;
        }
        
        .nav-item:hover {
            background: rgba(139, 92, 246, 0.1);
            transform: translateX(4px);
        }
        
        .nav-item.active {
            background: linear-gradient(90deg, var(--accent-purple) 0%, var(--accent-cyan) 100%);
            color: white;
        }
        
        .button-primary {
            background: linear-gradient(90deg, var(--accent-purple) 0%, var(--accent-cyan) 100%);
            border: none;
            border-radius: 8px;
            padding: 12px 24px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .button-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
        }
        
        .button-secondary {
            background: transparent;
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 24px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .button-secondary:hover {
            border-color: var(--accent-purple);
            color: var(--accent-purple);
        }
        
        .card {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 24px;
            margin: 16px 0;
        }
        
        .input-field {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 12px 16px;
            color: var(--text-primary);
            width: 100%;
            transition: all 0.2s ease;
        }
        
        .input-field:focus {
            outline: none;
            border-color: var(--accent-purple);
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }
        
        .toggle-switch {
            position: relative;
            width: 48px;
            height: 24px;
            background: var(--bg-tertiary);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .toggle-switch.active {
            background: var(--accent-purple);
        }
        
        .toggle-switch::after {
            content: '';
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: all 0.2s ease;
        }
        
        .toggle-switch.active::after {
            transform: translateX(24px);
        }
        
        .dropdown {
            background: var(--bg-tertiary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            color: var(--text-primary);
        }
        
        .dropdown option {
            background: var(--bg-tertiary);
            color: var(--text-primary);
        }
        
        .status-online {
            color: var(--success);
        }
        
        .status-offline {
            color: var(--error);
        }
        
        .status-idle {
            color: var(--warning);
        }
        
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-track {
            background: var(--bg-tertiary);
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: var(--border-color);
            border-radius: 3px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: var(--accent-purple);
        }
        
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
    </style>
    
    <!-- Tailwind Config -->
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        'bg-primary': '#1a1a2e',
                        'bg-secondary': '#16213e',
                        'bg-tertiary': '#0f172a',
                        'accent-purple': '#8b5cf6',
                        'accent-cyan': '#06b6d4',
                        'text-primary': '#f8fafc',
                        'text-secondary': '#cbd5e1',
                        'text-muted': '#64748b',
                        'border-color': '#334155'
                    }
                }
            }
        }
    </script>
</head>
<body>
    <div id="root"></div>
    
    <!-- Main React Application -->
    <script type="text/babel">
        const { useState, useEffect, useCallback } = React;
        
        // Main App Component
        function App() {
            const [user, setUser] = useState(null);
            const [selectedGuild, setSelectedGuild] = useState(null);
            const [currentPage, setCurrentPage] = useState('dashboard');
            const [loading, setLoading] = useState(true);
            
            useEffect(() => {
                checkAuth();
            }, []);
            
            const checkAuth = async () => {
                try {
                    const response = await fetch('/api/user/me');
                    if (response.ok) {
                        const userData = await response.json();
                        setUser(userData);
                        if (userData.admin_guilds.length > 0) {
                            setSelectedGuild(userData.admin_guilds[0]);
                        }
                    }
                } catch (error) {
                    console.error('Auth check failed:', error);
                } finally {
                    setLoading(false);
                }
            };
            
            if (loading) {
                return <LoadingScreen />;
            }
            
            if (!user) {
                return <LoginPage />;
            }
            
            return (
                <div className="flex h-screen bg-bg-primary">
                    <Sidebar 
                        user={user}
                        selectedGuild={selectedGuild}
                        currentPage={currentPage}
                        onPageChange={setCurrentPage}
                        onGuildChange={setSelectedGuild}
                    />
                    <MainContent 
                        user={user}
                        selectedGuild={selectedGuild}
                        currentPage={currentPage}
                    />
                </div>
            );
        }
        
        // Loading Screen Component
        function LoadingScreen() {
            return (
                <div className="flex items-center justify-center h-screen bg-bg-primary">
                    <div className="text-center">
                        <div className="w-16 h-16 border-4 border-accent-purple border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                        <h2 className="text-xl font-semibold text-text-primary">Loading Dashboard...</h2>
                        <p className="text-text-secondary mt-2">Please wait while we prepare your experience</p>
                    </div>
                </div>
            );
        }
        
        // Login Page Component
        function LoginPage() {
            return (
                <div className="flex items-center justify-center h-screen bg-bg-primary">
                    <div className="card max-w-md w-full mx-4 text-center">
                        <div className="mb-8">
                            <div className="w-20 h-20 bg-gradient-to-br from-accent-purple to-accent-cyan rounded-full mx-auto mb-4 flex items-center justify-center">
                                <span className="text-2xl font-bold text-white">H</span>
                            </div>
                            <h1 className="text-3xl font-bold text-text-primary mb-2">Hina Bot Dashboard</h1>
                            <p className="text-text-secondary">Manage your Discord server with ease</p>
                        </div>
                        
                        <button 
                            className="button-primary w-full mb-4"
                            onClick={() => window.location.href = '/auth/login'}
                        >
                            Login with Discord
                        </button>
                        
                        <p className="text-text-muted text-sm">
                            You need administrator permissions in at least one server to access the dashboard.
                        </p>
                    </div>
                </div>
            );
        }
        
        // Sidebar Component
        function Sidebar({ user, selectedGuild, currentPage, onPageChange, onGuildChange }) {
            const [collapsed, setCollapsed] = useState(false);

            const menuItems = [
                { id: 'dashboard', name: 'Dashboard', icon: '📊' },
                { id: 'admin', name: 'Admin', icon: '⚙️' },
                { id: 'moderation', name: 'Moderation', icon: '🛡️' },
                { id: 'automod', name: 'AutoMod', icon: '🤖' },
                { id: 'logging', name: 'Logging', icon: '📝' },
                { id: 'music', name: 'Music', icon: '🎵' },
                { id: 'utility', name: 'Utility', icon: '🔧' },
                { id: 'settings', name: 'Settings', icon: '⚙️' }
            ];

            return (
                <div className={`sidebar flex flex-col h-full transition-all duration-300 ${collapsed ? 'w-16' : 'w-64'}`}>
                    {/* Header */}
                    <div className="p-4 border-b border-border-color">
                        <div className="flex items-center justify-between">
                            {!collapsed && (
                                <div className="flex items-center space-x-3">
                                    <div className="w-8 h-8 bg-gradient-to-br from-accent-purple to-accent-cyan rounded-lg flex items-center justify-center">
                                        <span className="text-white font-bold">H</span>
                                    </div>
                                    <span className="font-bold text-lg">Hina</span>
                                </div>
                            )}
                            <button
                                onClick={() => setCollapsed(!collapsed)}
                                className="p-2 hover:bg-bg-tertiary rounded-lg transition-colors"
                            >
                                {collapsed ? '→' : '←'}
                            </button>
                        </div>
                    </div>

                    {/* Guild Selector */}
                    {!collapsed && (
                        <div className="p-4 border-b border-border-color">
                            <select
                                className="dropdown w-full p-2"
                                value={selectedGuild?.id || ''}
                                onChange={(e) => {
                                    const guild = user.admin_guilds.find(g => g.id === e.target.value);
                                    onGuildChange(guild);
                                }}
                            >
                                {user.admin_guilds.map(guild => (
                                    <option key={guild.id} value={guild.id}>
                                        {guild.name}
                                    </option>
                                ))}
                            </select>
                        </div>
                    )}

                    {/* Navigation Menu */}
                    <nav className="flex-1 p-4 space-y-2">
                        {menuItems.map(item => (
                            <button
                                key={item.id}
                                onClick={() => onPageChange(item.id)}
                                className={`nav-item w-full flex items-center space-x-3 p-3 text-left ${
                                    currentPage === item.id ? 'active' : 'text-text-secondary hover:text-text-primary'
                                }`}
                            >
                                <span className="text-lg">{item.icon}</span>
                                {!collapsed && <span>{item.name}</span>}
                            </button>
                        ))}
                    </nav>

                    {/* User Info */}
                    <div className="p-4 border-t border-border-color">
                        <div className="flex items-center space-x-3">
                            <img
                                src={user.avatar_url}
                                alt={user.display_name}
                                className="w-8 h-8 rounded-full"
                            />
                            {!collapsed && (
                                <div className="flex-1 min-w-0">
                                    <p className="text-sm font-medium text-text-primary truncate">
                                        {user.display_name}
                                    </p>
                                    <button
                                        onClick={() => window.location.href = '/auth/logout'}
                                        className="text-xs text-text-muted hover:text-accent-purple"
                                    >
                                        Logout
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            );
        }

        // Main Content Component
        function MainContent({ user, selectedGuild, currentPage }) {
            const renderPage = () => {
                switch (currentPage) {
                    case 'dashboard':
                        return <DashboardPage selectedGuild={selectedGuild} />;
                    case 'admin':
                        return <AdminPage selectedGuild={selectedGuild} />;
                    case 'moderation':
                        return <ModerationPage selectedGuild={selectedGuild} />;
                    case 'automod':
                        return <AutoModPage selectedGuild={selectedGuild} />;
                    case 'logging':
                        return <LoggingPage selectedGuild={selectedGuild} />;
                    case 'music':
                        return <MusicPage selectedGuild={selectedGuild} />;
                    case 'utility':
                        return <UtilityPage selectedGuild={selectedGuild} />;
                    case 'settings':
                        return <SettingsPage selectedGuild={selectedGuild} />;
                    default:
                        return <DashboardPage selectedGuild={selectedGuild} />;
                }
            };

            return (
                <div className="flex-1 flex flex-col overflow-hidden">
                    {/* Header */}
                    <header className="bg-bg-secondary border-b border-border-color p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <h1 className="text-2xl font-bold text-text-primary capitalize">
                                    {currentPage}
                                </h1>
                                {selectedGuild && (
                                    <p className="text-text-secondary">
                                        Managing {selectedGuild.name}
                                    </p>
                                )}
                            </div>
                            <div className="flex items-center space-x-4">
                                <div className="flex items-center space-x-2">
                                    <div className="w-3 h-3 bg-success rounded-full"></div>
                                    <span className="text-sm text-text-secondary">Bot Online</span>
                                </div>
                            </div>
                        </div>
                    </header>

                    {/* Content */}
                    <main className="flex-1 overflow-auto p-6 scrollbar-thin">
                        {selectedGuild ? renderPage() : (
                            <div className="text-center py-12">
                                <h2 className="text-xl font-semibold text-text-primary mb-2">
                                    No Server Selected
                                </h2>
                                <p className="text-text-secondary">
                                    Please select a server from the sidebar to continue.
                                </p>
                            </div>
                        )}
                    </main>
                </div>
            );
        }

        // Dashboard Page Component
        function DashboardPage({ selectedGuild }) {
            const [stats, setStats] = useState(null);
            const [loading, setLoading] = useState(true);

            useEffect(() => {
                if (selectedGuild) {
                    fetchGuildStats();
                }
            }, [selectedGuild]);

            const fetchGuildStats = async () => {
                try {
                    const response = await fetch(`/api/guilds/${selectedGuild.id}/stats`);
                    if (response.ok) {
                        const data = await response.json();
                        setStats(data);
                    }
                } catch (error) {
                    console.error('Failed to fetch stats:', error);
                } finally {
                    setLoading(false);
                }
            };

            if (loading) {
                return <div className="text-center py-8">Loading dashboard...</div>;
            }

            return (
                <div className="space-y-6 fade-in">
                    {/* Stats Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <StatCard
                            title="Total Commands"
                            value={stats?.total_commands || 0}
                            icon="⚡"
                            color="from-accent-purple to-accent-cyan"
                        />
                        <StatCard
                            title="Mod Actions"
                            value={stats?.total_mod_actions || 0}
                            icon="🛡️"
                            color="from-green-500 to-emerald-500"
                        />
                        <StatCard
                            title="Active Users"
                            value="1,234"
                            icon="👥"
                            color="from-blue-500 to-cyan-500"
                        />
                        <StatCard
                            title="Uptime"
                            value="99.9%"
                            icon="⏱️"
                            color="from-orange-500 to-red-500"
                        />
                    </div>

                    {/* Recent Activity */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Recent Commands</h3>
                            <div className="space-y-3">
                                {stats?.command_usage?.slice(0, 5).map((cmd, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 bg-bg-tertiary rounded-lg">
                                        <span className="text-text-primary">{cmd.command}</span>
                                        <span className="text-accent-purple font-semibold">{cmd.usage_count}</span>
                                    </div>
                                )) || (
                                    <p className="text-text-secondary">No recent command usage</p>
                                )}
                            </div>
                        </div>

                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Moderation Activity</h3>
                            <div className="space-y-3">
                                {stats?.moderation_actions?.slice(0, 5).map((action, index) => (
                                    <div key={index} className="flex items-center justify-between p-3 bg-bg-tertiary rounded-lg">
                                        <span className="text-text-primary capitalize">{action.action_type}</span>
                                        <span className="text-accent-cyan font-semibold">{action.count}</span>
                                    </div>
                                )) || (
                                    <p className="text-text-secondary">No recent moderation activity</p>
                                )}
                            </div>
                        </div>
                    </div>

                    {/* Quick Actions */}
                    <div className="card">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">Quick Actions</h3>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <QuickActionButton
                                title="Set Prefix"
                                icon="⚙️"
                                onClick={() => {/* Handle action */}}
                            />
                            <QuickActionButton
                                title="Mute Role"
                                icon="🔇"
                                onClick={() => {/* Handle action */}}
                            />
                            <QuickActionButton
                                title="Log Channel"
                                icon="📝"
                                onClick={() => {/* Handle action */}}
                            />
                            <QuickActionButton
                                title="Auto Roles"
                                icon="🎭"
                                onClick={() => {/* Handle action */}}
                            />
                        </div>
                    </div>
                </div>
            );
        }

        // Stat Card Component
        function StatCard({ title, value, icon, color }) {
            return (
                <div className="card">
                    <div className="flex items-center justify-between">
                        <div>
                            <p className="text-text-secondary text-sm">{title}</p>
                            <p className="text-2xl font-bold text-text-primary">{value}</p>
                        </div>
                        <div className={`w-12 h-12 rounded-lg bg-gradient-to-br ${color} flex items-center justify-center`}>
                            <span className="text-xl">{icon}</span>
                        </div>
                    </div>
                </div>
            );
        }

        // Quick Action Button Component
        function QuickActionButton({ title, icon, onClick }) {
            return (
                <button
                    onClick={onClick}
                    className="p-4 bg-bg-tertiary hover:bg-bg-secondary border border-border-color rounded-lg transition-all duration-200 hover:border-accent-purple group"
                >
                    <div className="text-2xl mb-2 group-hover:scale-110 transition-transform">{icon}</div>
                    <div className="text-sm text-text-secondary group-hover:text-accent-purple">{title}</div>
                </button>
            );
        }

        // Admin Page Component
        function AdminPage({ selectedGuild }) {
            const [settings, setSettings] = useState([]);
            const [prefixes, setPrefixes] = useState([]);
            const [loading, setLoading] = useState(true);

            useEffect(() => {
                if (selectedGuild) {
                    fetchAdminSettings();
                }
            }, [selectedGuild]);

            const fetchAdminSettings = async () => {
                try {
                    const [settingsRes, prefixesRes] = await Promise.all([
                        fetch(`/api/guilds/${selectedGuild.id}/settings`),
                        fetch(`/api/guilds/${selectedGuild.id}/prefixes`)
                    ]);

                    if (settingsRes.ok) {
                        const settingsData = await settingsRes.json();
                        setSettings(settingsData);
                    }

                    if (prefixesRes.ok) {
                        const prefixData = await prefixesRes.json();
                        setPrefixes(prefixData.prefixes);
                    }
                } catch (error) {
                    console.error('Failed to fetch admin settings:', error);
                } finally {
                    setLoading(false);
                }
            };

            if (loading) {
                return <div className="text-center py-8">Loading admin settings...</div>;
            }

            return (
                <div className="space-y-6 fade-in">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Prefix Management */}
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Server Prefixes</h3>
                            <PrefixManager
                                guildId={selectedGuild.id}
                                prefixes={prefixes}
                                onUpdate={setPrefixes}
                            />
                        </div>

                        {/* Basic Settings */}
                        <div className="card">
                            <h3 className="text-lg font-semibold text-text-primary mb-4">Basic Settings</h3>
                            <SettingsForm
                                guildId={selectedGuild.id}
                                settings={settings.filter(s => s.category === 'general')}
                                onUpdate={fetchAdminSettings}
                            />
                        </div>
                    </div>

                    {/* Mass Actions */}
                    <div className="card">
                        <h3 className="text-lg font-semibold text-text-primary mb-4">Mass Actions</h3>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <ActionButton title="Mass Dehoist" description="Remove hoisting characters from nicknames" />
                            <ActionButton title="Mass Rename" description="Reset all user nicknames" />
                            <ActionButton title="Mass Role" description="Add/remove roles from multiple users" />
                            <ActionButton title="Server Lock" description="Lock/unlock all channels" />
                        </div>
                    </div>
                </div>
            );
        }

        // Prefix Manager Component
        function PrefixManager({ guildId, prefixes, onUpdate }) {
            const [newPrefix, setNewPrefix] = useState('');
            const [loading, setLoading] = useState(false);

            const addPrefix = async () => {
                if (!newPrefix.trim()) return;

                setLoading(true);
                try {
                    const response = await fetch(`/api/guilds/${guildId}/prefixes`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ prefixes: [newPrefix.trim()] })
                    });

                    if (response.ok) {
                        onUpdate([...prefixes, newPrefix.trim()]);
                        setNewPrefix('');
                    }
                } catch (error) {
                    console.error('Failed to add prefix:', error);
                } finally {
                    setLoading(false);
                }
            };

            const removePrefix = async (prefix) => {
                try {
                    const response = await fetch(`/api/guilds/${guildId}/prefixes/${encodeURIComponent(prefix)}`, {
                        method: 'DELETE'
                    });

                    if (response.ok) {
                        onUpdate(prefixes.filter(p => p !== prefix));
                    }
                } catch (error) {
                    console.error('Failed to remove prefix:', error);
                }
            };

            return (
                <div className="space-y-4">
                    <div className="flex space-x-2">
                        <input
                            type="text"
                            value={newPrefix}
                            onChange={(e) => setNewPrefix(e.target.value)}
                            placeholder="Enter new prefix..."
                            className="input-field flex-1"
                            onKeyPress={(e) => e.key === 'Enter' && addPrefix()}
                        />
                        <button
                            onClick={addPrefix}
                            disabled={loading || !newPrefix.trim()}
                            className="button-primary px-4"
                        >
                            Add
                        </button>
                    </div>

                    <div className="space-y-2">
                        {prefixes.map((prefix, index) => (
                            <div key={index} className="flex items-center justify-between p-3 bg-bg-tertiary rounded-lg">
                                <code className="text-accent-purple">{prefix}</code>
                                <button
                                    onClick={() => removePrefix(prefix)}
                                    className="text-error hover:text-red-400 transition-colors"
                                >
                                    ✕
                                </button>
                            </div>
                        ))}
                        {prefixes.length === 0 && (
                            <p className="text-text-secondary text-center py-4">No custom prefixes set</p>
                        )}
                    </div>
                </div>
            );
        }

        // Settings Form Component
        function SettingsForm({ guildId, settings, onUpdate }) {
            const [values, setValues] = useState({});
            const [loading, setLoading] = useState(false);

            useEffect(() => {
                const initialValues = {};
                settings.forEach(setting => {
                    initialValues[setting.key] = setting.value || setting.default_value;
                });
                setValues(initialValues);
            }, [settings]);

            const updateSetting = async (key, value) => {
                setLoading(true);
                try {
                    const response = await fetch(`/api/guilds/${guildId}/settings/${key}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ key, value })
                    });

                    if (response.ok) {
                        setValues(prev => ({ ...prev, [key]: value }));
                        onUpdate();
                    }
                } catch (error) {
                    console.error('Failed to update setting:', error);
                } finally {
                    setLoading(false);
                }
            };

            return (
                <div className="space-y-4">
                    {settings.map(setting => (
                        <div key={setting.key} className="space-y-2">
                            <label className="block text-sm font-medium text-text-primary">
                                {setting.name}
                            </label>
                            <p className="text-xs text-text-secondary">{setting.description}</p>

                            {setting.type === 'boolean' ? (
                                <div
                                    className={`toggle-switch ${values[setting.key] ? 'active' : ''}`}
                                    onClick={() => updateSetting(setting.key, !values[setting.key])}
                                />
                            ) : setting.type === 'string' ? (
                                <input
                                    type="text"
                                    value={values[setting.key] || ''}
                                    onChange={(e) => setValues(prev => ({ ...prev, [setting.key]: e.target.value }))}
                                    onBlur={(e) => updateSetting(setting.key, e.target.value)}
                                    className="input-field"
                                    placeholder={setting.default_value}
                                />
                            ) : setting.type === 'channel' || setting.type === 'role' ? (
                                <select
                                    value={values[setting.key] || ''}
                                    onChange={(e) => updateSetting(setting.key, e.target.value)}
                                    className="dropdown w-full p-2"
                                >
                                    <option value="">Select {setting.type}...</option>
                                    {/* Options would be populated from API */}
                                </select>
                            ) : null}
                        </div>
                    ))}
                </div>
            );
        }

        // Action Button Component
        function ActionButton({ title, description, onClick }) {
            return (
                <button
                    onClick={onClick}
                    className="p-4 bg-bg-tertiary hover:bg-bg-secondary border border-border-color rounded-lg transition-all duration-200 hover:border-accent-purple text-left group"
                >
                    <h4 className="font-semibold text-text-primary group-hover:text-accent-purple mb-1">
                        {title}
                    </h4>
                    <p className="text-xs text-text-secondary">{description}</p>
                </button>
            );
        }

        // Placeholder components for other pages
        function ModerationPage({ selectedGuild }) {
            return (
                <div className="card">
                    <h2 className="text-xl font-semibold text-text-primary mb-4">Moderation Tools</h2>
                    <p className="text-text-secondary">Moderation features coming soon...</p>
                </div>
            );
        }

        function AutoModPage({ selectedGuild }) {
            return (
                <div className="card">
                    <h2 className="text-xl font-semibold text-text-primary mb-4">AutoMod Configuration</h2>
                    <p className="text-text-secondary">AutoMod features coming soon...</p>
                </div>
            );
        }

        function LoggingPage({ selectedGuild }) {
            return (
                <div className="card">
                    <h2 className="text-xl font-semibold text-text-primary mb-4">Logging Settings</h2>
                    <p className="text-text-secondary">Logging configuration coming soon...</p>
                </div>
            );
        }

        function MusicPage({ selectedGuild }) {
            return (
                <div className="card">
                    <h2 className="text-xl font-semibold text-text-primary mb-4">Music Settings</h2>
                    <p className="text-text-secondary">Music configuration coming soon...</p>
                </div>
            );
        }

        function UtilityPage({ selectedGuild }) {
            return (
                <div className="card">
                    <h2 className="text-xl font-semibold text-text-primary mb-4">Utility Commands</h2>
                    <p className="text-text-secondary">Utility features coming soon...</p>
                </div>
            );
        }

        function SettingsPage({ selectedGuild }) {
            return (
                <div className="card">
                    <h2 className="text-xl font-semibold text-text-primary mb-4">Bot Settings</h2>
                    <p className="text-text-secondary">Bot configuration coming soon...</p>
                </div>
            );
        }

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
